import chromadb
import os
import requests
from typing import List, Dict, Any, Optional
import voyageai
import anthropic
import json
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, as_completed
from tqdm import tqdm
import threading
from chromadb.utils import embedding_functions
from chromadb.api.types import Documents, EmbeddingFunction
import uuid

default_ef = embedding_functions.DefaultEmbeddingFunction()

class MemoryClient:
    def __init__(self):
        self.client = chromadb.Client()
        self.collections = {}

    def get_or_create_collection(self, name, **kwargs):
        if name not in self.collections:
            self.collections[name] = self.client.get_or_create_collection(name=name, **kwargs)
        return self.collections[name]

class ContextualVectorDB:
    def __init__(self, config):
        self.embeddings = None
        self.metadata = None
        self.db_path = config.get("path", ".")
        self.embedding_function = config.get("embedding_function", default_ef)
        self.chroma_client = chromadb.PersistentClient(path=self.db_path)
        collection_metadata = config.get("collection_metadata", None)
        self.collections = {}
        anthropic_api_key = config.get("anthropic_api_key")
        if anthropic_api_key is None:
            anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")        
        self.anthropic_client = anthropic.Client(api_key=anthropic_api_key)
        self.token_lock = threading.Lock()
        self.token_counts = {
            'input': 0,
            'output': 0,
            'cache_read': 0,
            'cache_creation': 0
        }
        self.documentation_collection = self.chroma_client.get_or_create_collection(
            name="documentation",
            embedding_function=self.embedding_function,
            metadata=collection_metadata,
        )

    def generate_embedding(self, data: str) -> List[float]:
        """
        Generate embeddings for the given text using the configured embedding function.
        
        Args:
            data: The text to generate embeddings for.
            
        Returns:
            A list of floats representing the embedding vector.
        """
        # Use the embedding function that was passed in the config
        embedding = self.embedding_function([data])
        if isinstance(embedding, list) and len(embedding) == 1:
            return embedding[0]
        return embedding


    def situate_context(self, doc: str, chunk: str) -> tuple[str, Any]:
        DOCUMENT_CONTEXT_PROMPT = """
        <document>
        {doc_content}
        </document>
        """

        CHUNK_CONTEXT_PROMPT = """
        Here is the chunk we want to situate within the whole document
        <chunk>
        {chunk_content}
        </chunk>

        Please give a short succinct context to situate this chunk within the overall document for the purposes of improving search retrieval of the chunk.
        Answer only with the succinct context and nothing else.
        """
        prompt = f"{DOCUMENT_CONTEXT_PROMPT.format(doc_content=doc)}\n\n{CHUNK_CONTEXT_PROMPT.format(chunk_content=chunk)}"
        
        # Standard Anthropic API call without prompt caching
        response = self.anthropic_client.messages.create(
            model="claude-3-haiku-20240307",
            max_tokens=1000,
            temperature=0.0,
            messages=[
                {
                    "role": "user", 
                    "content": prompt
                }
            ]
        )
        
        # Extract the text from the response
        response_text = response.content[0].text
        
        # Create a usage object similar to what was expected
        usage = {
            "input_tokens": response.usage.input_tokens,
            "output_tokens": response.usage.output_tokens
        }
        
        return response_text, usage

    def load_data(self, dataset: List[Dict[str, Any]], parallel_threads: int = 1):
        if self.embeddings and self.metadata:
            print("Vector database is already loaded. Skipping data loading.")
            return

        texts_to_embed = []
        metadata = []
        total_chunks = sum(len(doc['chunks']) for doc in dataset)

        def process_chunk(doc, chunk):
            #for each chunk, produce the context
            contextualized_text, usage = self.situate_context(doc['content'], chunk['content'])
            with self.token_lock:
                self.token_counts['input'] += usage['input_tokens']
                self.token_counts['output'] += usage['output_tokens']
                # These fields may not exist in the new usage object, so set to 0
                self.token_counts['cache_read'] += 0
                self.token_counts['cache_creation'] += 0            
            return {
                #append the context to the original text chunk
                'text_to_embed': f"{chunk['content']}\n\n{contextualized_text}",
                'metadata': {
                    'doc_id': doc['doc_id'],
                    'original_uuid': doc['original_uuid'],
                    'chunk_id': chunk['chunk_id'],
                    'original_index': chunk['original_index'],
                    'original_content': chunk['content'],
                    'contextualized_content': contextualized_text
                }
            }

        print(f"Processing {total_chunks} chunks with {parallel_threads} threads")
        with ThreadPoolExecutor(max_workers=parallel_threads) as executor:
            futures = []
            for doc in dataset:
                for chunk in doc['chunks']:
                    futures.append(executor.submit(process_chunk, doc, chunk))                    
            
            for future in tqdm(as_completed(futures), total=total_chunks, desc="Processing chunks"):
                result = future.result()
                texts_to_embed.append(result['text_to_embed'])
                metadata.append(result['metadata'])
        
        # Add all documents after processing is complete
        for text in texts_to_embed:
            self.add_documentation(text)


        #logging token usage
        print(f"Contextual Vector database loaded and saved. Total chunks processed: {len(texts_to_embed)}")
        print(f"Total input tokens without caching: {self.token_counts['input']}")
        print(f"Total output tokens: {self.token_counts['output']}")
        print(f"Total input tokens written to cache: {self.token_counts['cache_creation']}")
        print(f"Total input tokens read from cache: {self.token_counts['cache_read']}")
        
        total_tokens = self.token_counts['input'] + self.token_counts['cache_read'] + self.token_counts['cache_creation']
        savings_percentage = (self.token_counts['cache_read'] / total_tokens) * 100 if total_tokens > 0 else 0
        print(f"Total input token savings from prompt caching: {savings_percentage:.2f}% of all input tokens used were read from cache.")
        print("Tokens read from cache come at a 90 percent discount!")
    
    def add_documentation(self, documentation: str) -> str:
        doc_id = str(uuid.uuid4())
        # Generate embeddings using the embedding function that was configured for this collection
        # The documentation_collection already has the embedding_function set in __init__
        self.documentation_collection.add(
            documents=[documentation],  # Must be a list
            ids=[doc_id],              # Must be a list
        )
        return doc_id

    def get_or_create_collection(self, name, **kwargs):
        if name not in self.collections:
            self.collections[name] = self.chroma_client.get_or_create_collection(name=name, **kwargs)
        return self.collections[name]


class OpenRouterClient:
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.environ.get("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError("OpenRouter API key is required. Set it as OPENROUTER_API_KEY environment variable or pass it to the constructor.")
        self.api_base = "https://openrouter.ai/api/v1"
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
    
    def generate(self, prompt: str, model: str = "qwen/qwen3-235b-a22b", max_tokens: int = 500):
        url = f"{self.api_base}/chat/completions"
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens
        }
        
        response = requests.post(url, json=payload, headers=self.headers)
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"]

class EnhancedChromaDBQuery:
    def __init__(self, chroma_client, openrouter_client):
        self.chroma_client = chroma_client(config={"path": "."})
        self.openrouter_client = openrouter_client

    def add_to_chroma(self, collection_name: str, documents: List[str]):
        collection = self.chroma_client.get_or_create_collection(name=collection_name)
        collection.add(documents=documents, embeddings=self.client.embeddings(documents))
        return collection
    
    def query_with_llm_enhancement(self, collection_name: str, query_text: str, n_results: int = 3):
        # Get the collection
        collection = self.chroma_client.get_or_create_collection(name=collection_name)
        
        # Query ChromaDB using only query_texts to let ChromaDB use its default embedding function
        # This will generate embeddings with the same dimension as what's in the collection
        results = collection.query(
            query_texts=[query_text],
            n_results=n_results
        )
        
        # Extract documents
        documents = results.get("documents", [[]])[0]
        
        if not documents:
            return {"results": [], "llm_summary": "No relevant documents found."}
        
        # Create a prompt for the LLM
        prompt = f"""I have queried a vector database with the question: '{query_text}'
        
        The database returned these documents:
        
        {documents}
        
        Based on these documents, please provide a comprehensive answer to the original question.
        If the documents don't contain relevant information, please state that."""
        
        # Get LLM response
        llm_response = self.openrouter_client.generate(prompt)
        
        return {
            "raw_results": results,
            "documents": documents,
            "llm_summary": llm_response
        }

class VoyageAIEmbeddingFunction(EmbeddingFunction):
    def __init__(self, api_key=None, model_name="voyage-2", dimensions=1024):
        """
        Initialize the VoyageAI embedding function.
        
        Args:
            api_key: Your VoyageAI API key. If None, it will try to use the VOYAGE_API_KEY environment variable.
            model_name: The model to use for embeddings. Default is "voyage-2".
            dimensions: The dimension of the embeddings. Default is 1024 for voyage-2.
        """
        self.api_key = api_key or os.environ.get("VOYAGE_API_KEY")
        if not self.api_key:
            raise ValueError("VoyageAI API key is required. Set it as VOYAGE_API_KEY environment variable or pass it to the constructor.")
        
        self.client = voyageai.Client(api_key=self.api_key)
        self.model_name = model_name
        self.dimensions = dimensions
    
    def __call__(self, texts: Documents) -> list:
        """
        Generate embeddings for the given texts.
        
        Args:
            texts: A list of strings to generate embeddings for.
            
        Returns:
            A list of embeddings, one for each text.
        """
        if not texts:
            return []
        
        try:
            # For a single text, we need to handle it differently
            if len(texts) == 1:
                # Generate embedding for a single text
                embedding = self.client.embed(
                    text=texts[0],  # Pass as a single text, not a list
                    model=self.model_name,
                    input_type="document"
                )
                
                # Convert to list and return as a list of lists (with one element)
                if hasattr(embedding, 'tolist'):
                    return [embedding.tolist()]
                else:
                    return [list(embedding)]
            else:
                # For multiple texts, use batch processing
                # Process each text individually to avoid the subscriptable error
                result = []
                for text in texts:
                    embedding = self.client.embed(
                        text=text,  # Process one at a time
                        model=self.model_name,
                        input_type="document"
                    )
                    
                    # Convert to list
                    if hasattr(embedding, 'tolist'):
                        result.append(embedding.tolist())
                    else:
                        result.append(list(embedding))
                        
                return result
                
        except Exception as e:
            print(f"Error generating embeddings with VoyageAI: {str(e)}")
            # Return empty embeddings of the right dimension as a fallback
            return [[0.0] * self.dimensions for _ in texts]

if __name__ == "__main__":
    # Initialize ChromaDB clients
    # Load the transformed dataset
    with open('training_data/codebase_chunks.json', 'r') as f:
        transformed_dataset = json.load(f)

    voyage_ef = VoyageAIEmbeddingFunction(
    api_key="pa-XWZrnUVBAvjApR7Xr540rux79iPWoq4Jtwc2eEUaKBg",  # Or pass your API key directly
    model_name="voyage-3-lite"  # You can use other models like "voyage-large-2" if needed
    )

    # Initialize the vector database
    contextual_db = ContextualVectorDB(config={"path": "./test_chromadb", "embedding_function": voyage_ef, "anthropic_api_key": os.environ.get("ANTHROPIC_API_KEY")})
   
    # Load and process the data
    #note: consider increasing the number of parallel threads to run this faster, or reducing the number of parallel threads if concerned about hitting your API rate limit
    contextual_db.load_data(transformed_dataset, parallel_threads=5)   

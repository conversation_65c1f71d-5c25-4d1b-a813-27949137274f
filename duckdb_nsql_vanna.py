import os
import sys

# Import the DuckDB-NSQL singleton implementation
from duckdb_nsql_implementation import DuckDBNSQL_Singleton

# Import Vanna components
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
import vanna
from vanna.base.base import VannaBase

# Monkey patch to prevent kaleido import
sys.modules['kaleido'] = type('', (), {})()
sys.modules['kaleido.scopes'] = type('', (), {})()
sys.modules['kaleido.scopes.plotly'] = type('', (), {'PlotlyScope': type('', (), {'render': lambda *args, **kwargs: b''})})

# Set environment variable for plotly
os.environ["PLOTLY_RENDERER"] = "svg"

# Create a DuckDB-NSQL Chat class that implements VannaBase
class DuckDBNSQL_Chat(VannaBase):
    def __init__(self, config=None):
        VannaBase.__init__(self, config=config)
        
        # Default parameters - can be overridden using config
        self.temperature = 0.1
        self.max_new_tokens = 1024
        
        if config is not None:
            if "temperature" in config:
                self.temperature = config["temperature"]
            
            if "max_new_tokens" in config:
                self.max_new_tokens = config["max_new_tokens"]
        
        # Get the singleton instance of DuckDB-NSQL-7B model
        self.model = DuckDBNSQL_Singleton.get_instance(config=config)
    
    # Implement the required abstract methods
    def system_message(self, message):
        return {"role": "system", "content": message}
    
    def user_message(self, message):
        return {"role": "user", "content": message}
    
    def assistant_message(self, message):
        return {"role": "assistant", "content": message}
    
    def submit_prompt(self, prompt, **kwargs):
        """Submit a prompt to the DuckDB-NSQL model and get a response."""
        if not prompt:
            raise ValueError("Prompt is empty or None")
        
        # Format the prompt for the DuckDB-NSQL model
        formatted_prompt = ""
        for message in prompt:
            if isinstance(message, dict) and "role" in message and "content" in message:
                role = message["role"]
                content = message["content"]
                
                if role == "system":
                    formatted_prompt += f"### System:\n{content}\n\n"
                elif role == "user":
                    formatted_prompt += f"### User:\n{content}\n\n"
                elif role == "assistant":
                    formatted_prompt += f"### Assistant:\n{content}\n\n"
            else:
                # If it's just a string, treat it as user message
                formatted_prompt += f"### User:\n{message}\n\n"
        
        # Add the final assistant prompt
        formatted_prompt += "### Assistant:\n"
        
        # Generate response
        return self.model.generate_response(
            formatted_prompt,
            max_new_tokens=self.max_new_tokens,
            temperature=self.temperature
        )
    
    # Implement required prompt generation methods
    def get_sql_prompt(self, question, similar_questions=None, ddl=None, documentation=None, **kwargs):
        """Generate a prompt for SQL generation."""
        prompt = []
        
        # System message with instructions
        system_message = "You are an AI assistant that generates SQL queries based on natural language questions. "
        system_message += f"Use {self.dialect} dialect. "
        
        # Add information about the database schema if available
        if ddl:
            system_message += "\n\nHere is the database schema:\n```sql\n"
            for d in ddl:
                system_message += d + "\n"
            system_message += "```"
        
        # Add documentation if available
        if documentation:
            system_message += "\n\nHere is additional documentation about the database:\n"
            for doc in documentation:
                system_message += doc + "\n"
        
        prompt.append(self.system_message(system_message))
        
        # Add examples of similar questions if available
        if similar_questions and len(similar_questions) > 0:
            examples_message = "Here are some examples of similar questions and their SQL queries:\n"
            
            for i, sq in enumerate(similar_questions):
                if isinstance(sq, dict) and "question" in sq and "sql" in sq:
                    examples_message += f"Question: {sq['question']}\n"
                    examples_message += f"SQL: ```sql\n{sq['sql']}\n```\n\n"
            
            prompt.append(self.user_message(examples_message))
            prompt.append(self.assistant_message("I'll use these examples to help me generate SQL for your question."))
        
        # Add the actual question
        prompt.append(self.user_message(f"Generate SQL for this question: {question}"))
        
        return prompt

# Create a custom Vanna class with DuckDB-NSQL-7B model and vector store
class MyVanna(ChromaDB_VectorStore, DuckDBNSQL_Chat):
    def __init__(self, config=None):
        if config is None:
            config = {}
        
        # Configure the DuckDB-NSQL-7B model
        config["temperature"] = 0.1  # Lower temperature for more deterministic SQL generation
        config["max_new_tokens"] = 1024  # Maximum tokens to generate
        config["device"] = "cpu"  # Force CPU usage
        
        # Initialize both parent classes
        ChromaDB_VectorStore.__init__(self, config=config)
        DuckDBNSQL_Chat.__init__(self, config=config)

# Create a singleton class for the embedding function
class EmbeddingFunctionSingleton:
    _instance = None
    _model_path = './embedding_model_cache'
    _model_name = "intfloat/multilingual-e5-base"
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            print("Loading embedding model for the first time...")
            cls._instance = SentenceTransformerEmbeddingFunction(model_name=cls._model_name, cache_folder=cls._model_path)
            print("Embedding model loaded successfully.")
        return cls._instance

# Example usage
def main():
    # Get the embedding function instance
    ef = EmbeddingFunctionSingleton.get_instance()
    
    # Create an instance of our custom Vanna class
    vn = MyVanna(config={
        "embedding_function": ef,
        "n_results": 5  # Number of similar examples to retrieve
    })
    
    # Define a simple database schema for testing
    schema_sql = """
    CREATE TABLE taxi (
        VendorID bigint,
        tpep_pickup_datetime timestamp,
        tpep_dropoff_datetime timestamp,
        passenger_count double,
        trip_distance double,
        fare_amount double,
        extra double,
        tip_amount double,
        tolls_amount double,
        improvement_surcharge double,
        total_amount double
    );
    """
    
    # Train the model with the schema
    print("Training with schema...")
    vn.train(ddl=schema_sql)
    
    # Test the model with a question
    question = "get all columns ending with _amount from taxi table"
    print(f"\nGenerating SQL for: {question}")
    
    try:
        # Generate SQL for the question
        sql = vn.generate_sql(question)
        print(f"\nGenerated SQL: {sql}")
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main()

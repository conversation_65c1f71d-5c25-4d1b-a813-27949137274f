import os
import sys
import json

from openai import OpenAI
from vanna.openai.openai_chat import OpenAI_Chat
from vanna.duckdb_nsql.duckdb_nsql_chat import DuckDBNSQL_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
import os.path

import vanna
from vanna.flask import VannaFlaskApp
from src.trainfile import Train
import voyageai
from chromadb.api.types import Documents, EmbeddingFunction
from vanna.ollama.ollama import Ollama


# Monkey patch to prevent kaleido import
sys.modules['kaleido'] = type('', (), {})()
sys.modules['kaleido.scopes'] = type('', (), {})()
sys.modules['kaleido.scopes.plotly'] = type('', (), {'PlotlyScope': type('', (), {'render': lambda *args, **kwargs: b''})})

# Set environment variable for plotly
os.environ["PLOTLY_RENDERER"] = "svg"

# Now import vanna components

openai_client = OpenAI(api_key='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', base_url="https://openrouter.ai/api/v1")

# Create a custom Vanna class with your preferred LLM and vector store
class MyVanna(ChromaDB_VectorStore, Ollama):
    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        Ollama.__init__(self, config=config)

# Create a singleton class for the embedding function to avoid reloading the model each time
class EmbeddingFunctionSingleton:
    _instance = None
    _model_path = './embedding_model_cache'
    _model_name = "intfloat/multilingual-e5-base"
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            print("Loading embedding model for the first time...")
            cls._instance = SentenceTransformerEmbeddingFunction(model_name=cls._model_name, cache_folder=cls._model_path)
            print("Embedding model loaded successfully.")
        return cls._instance


class VoyageAIEmbeddingFunction(EmbeddingFunction):
    def __init__(self, api_key=None, model_name="voyage-2", dimensions=1024):
        """
        Initialize the VoyageAI embedding function.
        
        Args:
            api_key: Your VoyageAI API key. If None, it will try to use the VOYAGE_API_KEY environment variable.
            model_name: The model to use for embeddings. Default is "voyage-2".
            dimensions: The dimension of the embeddings. Default is 1024 for voyage-2.
        """
        self.api_key = api_key or os.environ.get("VOYAGE_API_KEY")
        if not self.api_key:
            raise ValueError("VoyageAI API key is required. Set it as VOYAGE_API_KEY environment variable or pass it to the constructor.")
        
        self.client = voyageai.Client(api_key=self.api_key)
        self.model_name = model_name
        self.dimensions = dimensions
    
    def __call__(self, texts: Documents) -> list:
        """
        Generate embeddings for the given texts.
        
        Args:
            texts: A list of strings to generate embeddings for.
            
        Returns:
            A list of embeddings, one for each text.
        """
        if not texts:
            return []
        
        try:
            # For a single text, we need to handle it differently
            if len(texts) == 1:
                # Generate embedding for a single text
                embedding = self.client.embed(
                    text=texts[0],  # Pass as a single text, not a list
                    model=self.model_name,
                    input_type="document"
                )
                
                # Convert to list and return as a list of lists (with one element)
                if hasattr(embedding, 'tolist'):
                    return [embedding.tolist()]
                else:
                    return [list(embedding)]
            else:
                # For multiple texts, use batch processing
                # Process each text individually to avoid the subscriptable error
                result = []
                for text in texts:
                    embedding = self.client.embed(
                        text=text,  # Process one at a time
                        model=self.model_name,
                        input_type="document"
                    )
                    
                    # Convert to list
                    if hasattr(embedding, 'tolist'):
                        result.append(embedding.tolist())
                    else:
                        result.append(list(embedding))
                        
                return result
                
        except Exception as e:
            print(f"Error generating embeddings with VoyageAI: {str(e)}")
            # Return empty embeddings of the right dimension as a fallback
            return [[0.0] * self.dimensions for _ in texts]

# Get the embedding function instance
ef = EmbeddingFunctionSingleton.get_instance()

#voyage_ef = VoyageAIEmbeddingFunction(
#    api_key="pa-XWZrnUVBAvjApR7Xr540rux79iPWoq4Jtwc2eEUaKBg",  # Or pass your API key directly
#    model_name="voyage-code-2"  # You can use other models like "voyage-large-2" if needed
#)
# Initialize with your API key
#vn = MyVanna(config={'api_key': '************************************************************************************************************'
#                        , 'model': 'claude-3-7-sonnet-20250219'                        
#                        , 'path': './chroma_db'          
#                        , 'embedding_function': ef              
#                        })

# Initialize with your OpenRouter API key
vn = MyVanna(config={'api_key': '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'
                        , 'model': 'devstral:latest'                        
                        , 'path': './chroma_db'          
                        , 'embedding_function': ef
                        })


# Connect to your database
#vn.connect_to_postgres(
#    host='ep-shy-base-a1r7sb9g-pooler.ap-southeast-1.aws.neon.tech',
#    database='verceldb',
#    user='default',
#    password='sw5paT9xJLGY',
#    port=5432,
#    sslmode='require'
#)

vn.connect_to_postgres(
    host='************',
    dbname='clinical_db',
    user='nhri001',
    password='cbit2012',
    port=5432
)


# Define training database schema
training_schema_sql = """

CREATE TABLE public.sysmdlogs (
  isyslog48_ft TEXT DEFAULT gen_random_uuid() NOT NULL,
  login24_id TEXT,  # Login ID
  staff24_id TEXT,  # Staff ID
  role24_id TEXT,  # Role ID
  function24_id TEXT,  # Login action 值：LOGIN
  function128_tx TEXT,  # Function description 值：系統登鏘
  action24_id TEXT,  # login type 值：WINDOWS，WEB 
  action256_tx TEXT,  # Action description
  log24_dt TIMESTAMP WITHOUT TIME ZONE STORAGE PLAIN,  # Log date
  status8_cd TEXT,  # login Status code L:LOGIN
  status128_tx TEXT,  # login Status description 值：登入成功，失败
  ip48_tx TEXT,  # IP address
  iguid32_ft TEXT DEFAULT gen_random_uuid(),
  CONSTRAINT sysmdlogs_pkey PRIMARY KEY(isyslog48_ft)
) ;
"""

# Sample SQL queries for training
sample_queries = [
    {
        "question": "What are the top 5 login by order count?",
        "sql": """
SELECT u.username, COUNT(o.order_id) as order_count
FROM users u
JOIN orders o ON u.user_id = o.user_id
GROUP BY u.username
ORDER BY order_count DESC
LIMIT 5;
"""
    },
    {
        "question": "What is the average order amount by product category?",
        "sql": """
SELECT p.category, AVG(oi.price * oi.quantity) as avg_order_amount
FROM products p
JOIN order_items oi ON p.product_id = oi.product_id
GROUP BY p.category
ORDER BY avg_order_amount DESC;
"""
    },
    {
        "question": "Which products have never been ordered?",
        "sql": """
SELECT p.product_name
FROM products p
LEFT JOIN order_items oi ON p.product_id = oi.product_id
WHERE oi.item_id IS NULL;
"""
    },
    {
        "question": "What is the total revenue by month?",
        "sql": """
SELECT
    EXTRACT(YEAR FROM o.order_date) as year,
    EXTRACT(MONTH FROM o.order_date) as month,
    SUM(o.total_amount) as total_revenue
FROM orders o
GROUP BY year, month
ORDER BY year, month;
"""
    }
]

#vn.train(documentation="This is a documentation")

# Train the model with schema and sample queries
try:
    # First, create the schema
    #vn.run_sql(training_schema_sql)
    #print("Training schema created successfully")

    # Train with the schema DDL
    #vn.train(ddl=training_schema_sql
    #print("Trained with schema DDL")

    # Train with sample queries
    '''
    for query in sample_queries:
        vn.train(question=query["question"], sql=query["sql"])
        print(f"Trained with query: {query['question']}")
    '''

    # Option to load training data from files
    use_file_based_training = True
    if use_file_based_training:
        # Load schema from file
        schema_file_path = 'training_data/postgres_sample/schema.sql'
        if os.path.exists(schema_file_path):
            with open(schema_file_path, 'r') as f:
                schema_sql = f.read()
                # Execute the schema SQL
                vn.run_sql(schema_sql)
                # Train with the schema
                vn.train(ddl=schema_sql)
                print(f"Loaded and trained with schema from {schema_file_path}")

        # Load questions from JSON file
        questions_file_path = 'training_data/postgres_sample/questions.json'
        if os.path.exists(questions_file_path):
            import json
            with open(questions_file_path, 'r') as f:
                questions_data = json.load(f)
                # Train with each question-answer pair
                for qa_pair in questions_data:
                    vn.train(question=qa_pair["question"], sql=qa_pair["answer"])
                print(f"Loaded and trained with {len(questions_data)} questions from {questions_file_path}")

except Exception as e:
    print(f"Error during training: {str(e)}")

# To list all collections
collections = vn.chroma_client.list_collections()
print(collections)  # This will show all collection names
# To get details about specific collections
sql_collection = vn.sql_collection
ddl_collection = vn.ddl_collection
documentation_collection = vn.documentation_collection

# To see the count of items in each collection
print(f"SQL Collection count: {sql_collection.count()}")
print(f"DDL Collection count: {ddl_collection.count()}")
print(f"Documentation Collection count: {documentation_collection.count()}")

# To get all items from a collection
sql_data = vn.sql_collection.get()
print(sql_data)  # This returns a dictionary with keys: 'ids', 'embeddings', 'documents', 'metadatas'

# Function to train DLL type from JSON files in the training_data/dll_define directory


# Example usage of the new function
# Initialize the Tran class with our Vanna instance

train = Train(vn)

try:
    # Get schema for a table and save as JSON
    #train.get_table_schema_to_json('casedauqc')    
    # Train with DLL definitions from JSON files
    train.train_from_dll_define_json()
    print("Trained with DLL definitions from JSON files")
except Exception as e:
    print(f"Error during DLL training: {str(e)}")

# Disable figure as image to avoid kaleido dependency
vanna.fig_as_img = False



# Continue with your code
VannaFlaskApp(vn).run()

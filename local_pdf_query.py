import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# For environment variables
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

try:
    import openai
    import PyPDF2
    import chromadb
    from sentence_transformers import SentenceTransformer
    import numpy as np
except ImportError as e:
    print(f"Error importing required packages: {e}")
    print("Please install the required packages with: uv pip install openai pypdf2 chromadb python-dotenv sentence-transformers")
    sys.exit(1)

# Configure OpenAI client if API key is available
openai_client = None
if os.getenv("OPENAI_API_KEY"):
    openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

class PDFVectorStore:
    """
    A class to manage PDF documents using local embeddings and ChromaDB.
    Provides functionality to add PDFs and query them using semantic search.
    """

    def __init__(self, persist_dir: str = "./pdf_store", collection_name: str = "pdf_documents", model_name: str = "all-MiniLM-L6-v2"):
        """
        Initialize the PDF store with local embeddings and ChromaDB.

        Args:
            persist_dir (str): Directory to persist the ChromaDB
            collection_name (str): Name of the ChromaDB collection
            model_name (str): Name of the sentence-transformers model to use
        """
        self.persist_dir = persist_dir
        self.collection_name = collection_name
        self.chunk_size = 1000
        self.chunk_overlap = 200

        # Load embedding model
        self.model = SentenceTransformer(model_name)

        # Create directory if it doesn't exist
        os.makedirs(self.persist_dir, exist_ok=True)

        # Initialize ChromaDB client
        self.chroma_client = chromadb.PersistentClient(path=self.persist_dir)

        # Create or get collection
        self.collection = self.chroma_client.get_or_create_collection(
            name=self.collection_name,
            metadata={"hnsw:space": "cosine"}
        )

        logging.info(f"Initialized PDF Vector Store with collection: {self.collection_name}")

    def _get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for a text using sentence-transformers.

        Args:
            text (str): Text to embed

        Returns:
            List[float]: Embedding vector
        """
        try:
            embedding = self.model.encode(text)
            return embedding.tolist()
        except Exception as e:
            logging.error(f"Error getting embedding: {str(e)}")
            raise

    def _extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        Extract text from a PDF file.

        Args:
            pdf_path (str): Path to the PDF file

        Returns:
            str: Extracted text
        """
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text()

                # Clean the text
                text = ' '.join(text.split())
                return text
        except Exception as e:
            logging.error(f"Error extracting text from PDF: {str(e)}")
            raise

    def _chunk_text(self, text: str) -> List[str]:
        """
        Split text into chunks with overlap.

        Args:
            text (str): Text to split

        Returns:
            List[str]: List of text chunks
        """
        chunks = []
        for i in range(0, len(text), self.chunk_size - self.chunk_overlap):
            chunk = text[i:i + self.chunk_size]
            if len(chunk) > 100:  # Only add chunks with substantial content
                chunks.append(chunk)
        return chunks

    def add_pdf(self, pdf_path: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Add a PDF document to the vector store.

        Args:
            pdf_path (str): Path to the PDF file
            metadata (dict, optional): Additional metadata for the document

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if file exists and is a PDF
            path = Path(pdf_path)
            if not path.exists():
                logging.error(f"File not found: {pdf_path}")
                return False
            if path.suffix.lower() != ".pdf":
                logging.error(f"Not a PDF file: {pdf_path}")
                return False

            # Extract text from PDF
            logging.info(f"Extracting text from PDF: {pdf_path}")
            text = self._extract_text_from_pdf(pdf_path)

            # Split text into chunks
            chunks = self._chunk_text(text)
            logging.info(f"Split PDF into {len(chunks)} chunks")

            # Prepare metadata
            doc_metadata = {
                "source": pdf_path,
                "filename": path.name
            }
            if metadata:
                doc_metadata.update(metadata)

            # Add chunks to collection
            for i, chunk in enumerate(chunks):
                chunk_id = f"{path.stem}_{i}"
                chunk_metadata = doc_metadata.copy()
                chunk_metadata["chunk_index"] = i

                # Get embedding for chunk
                embedding = self._get_embedding(chunk)

                # Add to collection
                self.collection.add(
                    ids=[chunk_id],
                    embeddings=[embedding],
                    metadatas=[chunk_metadata],
                    documents=[chunk]
                )

                if (i + 1) % 10 == 0 or (i + 1) == len(chunks):
                    logging.info(f"Added {i + 1}/{len(chunks)} chunks")

            logging.info(f"Successfully added PDF: {pdf_path}")
            return True

        except Exception as e:
            logging.error(f"Error adding PDF: {str(e)}")
            return False

    def query(self, query_text: str, top_k: int = 5) -> Dict[str, Any]:
        """
        Query the vector store with a natural language query.

        Args:
            query_text (str): The query text
            top_k (int): Number of top results to return

        Returns:
            dict: Query results including response and source documents
        """
        try:
            # Get embedding for query
            query_embedding = self._get_embedding(query_text)

            # Query collection
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=["documents", "metadatas", "distances"]
            )

            # Format results
            documents = []
            for i in range(len(results["ids"][0])):
                doc = {
                    "text": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "score": 1.0 - results["distances"][0][i]  # Convert distance to similarity score
                }
                documents.append(doc)

            # Just return the most relevant chunks as the answer
            context = "\n\n".join([f"Relevant text {i+1} (score: {doc['score']:.2f}):\n{doc['text'][:500]}..." for i, doc in enumerate(documents)])
            answer = f"Here are the most relevant sections from the document:\n\n{context}"

            return {
                "response": answer,
                "source_documents": documents
            }

        except Exception as e:
            logging.error(f"Error executing query: {str(e)}")
            return {"error": str(e), "source_documents": []}

    def get_document_count(self) -> int:
        """
        Get the number of documents in the collection.

        Returns:
            int: Number of documents
        """
        return self.collection.count()

# Example usage
if __name__ == "__main__":
    try:
        # Initialize the PDF store
        print("Initializing PDF Vector Store...")
        pdf_store = PDFVectorStore()

        # Add a PDF file if specified
        if len(sys.argv) > 1 and sys.argv[1].endswith('.pdf'):
            pdf_path = sys.argv[1]
            path = Path(pdf_path)

            # Check if PDF is already in the collection
            existing_ids = pdf_store.collection.get(include=[])['ids']
            pdf_id_prefix = f"{path.stem}_"

            if any(id.startswith(pdf_id_prefix) for id in existing_ids):
                print(f"PDF already in collection: {pdf_path}")
            else:
                print(f"Adding PDF: {pdf_path}")
                pdf_store.add_pdf(pdf_path, {"category": "user_provided"})

        # Print document count
        print(f"Total documents in collection: {pdf_store.get_document_count()}")

        # Query if argument is provided
        if len(sys.argv) > 2:
            query = sys.argv[2]
            print(f"Querying: {query}")
            results = pdf_store.query(query)

            print("\nResponse:\n")
            if "error" in results:
                print(f"Error: {results['error']}")
            else:
                print(results["response"])

            print("\nSources:\n")
            for i, doc in enumerate(results.get("source_documents", [])):
                print(f"Source {i+1}:\n{doc['text'][:200]}...\n")
                print(f"Source file: {doc['metadata'].get('filename', 'Unknown')}\n")
        else:
            print("\nTo query the PDF store, run: python local_pdf_query.py \"your question here\"")

    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

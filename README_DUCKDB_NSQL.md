# Vanna with DuckDB-NSQL-7B Model

This implementation replaces the default LLM router in Vanna with the motherduckdb/DuckDB-NSQL-7B-v0.1 model using a singleton pattern for efficient memory usage.

## Features

- **Local LLM**: Uses the DuckDB-NSQL-7B model locally without requiring API keys
- **Singleton Pattern**: Loads the model only once to optimize memory usage
- **Memory Efficient**: Implements 4-bit quantization to reduce memory footprint
- **SQL Specialized**: Uses a model fine-tuned specifically for SQL generation
- **Seamless Integration**: Works with existing Vanna architecture

## Requirements

- Python 3.8+
- CUDA-compatible GPU (recommended) or CPU with sufficient memory (16GB+ RAM)
- Dependencies listed in `requirements_duckdb_nsql.txt`

## Installation

```bash
# Clone the repository (if you haven't already)
git clone https://github.com/your-username/vanna.git
cd vanna

# Install dependencies
pip install -r requirements_duckdb_nsql.txt
```

## Usage

### Basic Usage

```python
# Import the DuckDB-NSQL chat implementation
from src.vanna.duckdb_nsql.duckdb_nsql_chat import DuckDBNSQL_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore

# Create a custom Vanna class
class MyVanna(ChromaDB_VectorStore, DuckDBNSQL_Chat):
    def __init__(self, config=None):
        if config is None:
            config = {}
        
        # Configure the model parameters
        config["temperature"] = 0.1
        config["max_new_tokens"] = 1024
        
        # Initialize parent classes
        ChromaDB_VectorStore.__init__(self, config=config)
        DuckDBNSQL_Chat.__init__(self, config=config)

# Create an instance
vn = MyVanna()

# Connect to your database
vn.connect_to_postgres(...)

# Train with your schema
vn.train(ddl=your_schema_sql)

# Generate SQL for a question
sql = vn.generate_sql("What are the top 10 customers by sales?")
```

### Running the Example

We've provided an example script that demonstrates how to use the DuckDB-NSQL-7B model with Vanna:

```bash
python test_duckdb_nsql.py
```

The first time you run this, it will download the model which may take some time depending on your internet connection.

## Configuration Options

The DuckDB-NSQL implementation supports the following configuration options:

- `temperature` (default: 0.1): Controls randomness in generation. Lower values make output more deterministic.
- `max_new_tokens` (default: 1024): Maximum number of tokens to generate.
- `device` (auto-detected): Set to "cuda" for GPU or "cpu" for CPU.

## Memory Optimization

The DuckDB-NSQL-7B model is loaded with 4-bit quantization to reduce memory usage. This allows the model to run on systems with limited GPU memory.

## Troubleshooting

### Common Issues

1. **Out of Memory Error**: If you encounter CUDA out of memory errors, try:
   - Reducing batch size or sequence length
   - Using CPU instead of GPU by setting device="cpu" in config
   - Closing other applications to free up memory

2. **Slow Generation**: SQL generation might be slow on CPU. For better performance:
   - Use a CUDA-compatible GPU
   - Reduce max_new_tokens if you only need short SQL queries

3. **Model Download Issues**: If the model download fails:
   - Check your internet connection
   - Try downloading the model manually from Hugging Face

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the terms of the MIT license.

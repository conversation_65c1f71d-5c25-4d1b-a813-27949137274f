import os
import re
import json
import PyPDF2
from pathlib import Path
import pandas as pd

class Train:
    """
    A class to handle various training-related operations for Vanna.
    This includes training from PDFs, JSON schema definitions, and database schemas.
    """
    
    def __init__(self, vn_instance):
        """
        Initialize the Tran class with a Vanna instance.
        
        Args:
            vn_instance: An instance of Van<PERSON> to use for training
        """
        self.vn = vn_instance
    
    def train_from_pdf(self, pdf_path, chunk_size=1000, overlap=200):
        """
        Extract text from a PDF file and train <PERSON>na with it as documentation.
        
        Args:
            pdf_path (str): Path to the PDF file
            chunk_size (int): Size of text chunks to split the PDF content into
            overlap (int): Overlap between chunks to maintain context
            
        Returns:
            int: Number of chunks trained
        """
        try:
            # Check if file exists
            if not os.path.isfile(pdf_path):
                print(f"Error: PDF file {pdf_path} not found")
                return 0
                
            # Open PDF file
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                
                # Extract text from all pages
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"
                
                # Split text into chunks with overlap
                chunks = []
                for i in range(0, len(text), chunk_size - overlap):
                    chunk = text[i:i + chunk_size]
                    chunks.append(chunk)
                
                # Train Vanna with each chunk
                for i, chunk in enumerate(chunks):
                    self.vn.train(documentation=chunk)
                    print(f"Trained chunk {i+1}/{len(chunks)} from {os.path.basename(pdf_path)}")
                
                print(f"Successfully trained {len(chunks)} chunks from {pdf_path}")
                return len(chunks)
                
        except Exception as e:
            print(f"Error processing PDF {pdf_path}: {str(e)}")
            return 0
    
    def train_from_pdf_directory(self, directory_path, chunk_size=1000, overlap=200):
        """
        Train Vanna with all PDF files in a directory.
        
        Args:
            directory_path (str): Path to the directory containing PDF files
            chunk_size (int): Size of text chunks to split the PDF content into
            overlap (int): Overlap between chunks to maintain context
            
        Returns:
            tuple: (Number of PDFs processed, Total number of chunks trained)
        """
        try:
            # Check if directory exists
            if not os.path.isdir(directory_path):
                print(f"Error: Directory {directory_path} does not exist")
                return (0, 0)
                
            # Get all PDF files in the directory
            pdf_files = list(Path(directory_path).glob('**/*.pdf'))
            
            if not pdf_files:
                print(f"No PDF files found in {directory_path}")
                return (0, 0)
                
            print(f"Found {len(pdf_files)} PDF files in {directory_path}")
            
            # Process each PDF file
            total_chunks = 0
            processed_files = 0
            
            for pdf_file in pdf_files:
                chunks = self.train_from_pdf(str(pdf_file), chunk_size, overlap)
                if chunks > 0:
                    total_chunks += chunks
                    processed_files += 1
            
            print(f"Successfully processed {processed_files}/{len(pdf_files)} PDF files with {total_chunks} total chunks")
            return (processed_files, total_chunks)
            
        except Exception as e:
            print(f"Error processing PDF directory: {str(e)}")
            return (0, 0)
    
    def train_from_dll_define_json(self, directory_path='training_data/dll_define'):
        """
        Train Vanna with table definitions from JSON files in the specified directory.
        These JSON files contain table definitions with columns, types, constraints, and labels.
        
        Args:
            directory_path (str): Path to the directory containing JSON definition files
            
        Returns:
            int: Number of JSON files processed
        """
        try:
            # Check if directory exists
            if not os.path.isdir(directory_path):
                print(f"Error: Directory {directory_path} does not exist")
                return 0
                
            # Get all JSON files in the directory
            json_files = list(Path(directory_path).glob('**/*.json'))
            
            if not json_files:
                print(f"No JSON files found in {directory_path}")
                return 0
                
            print(f"Found {len(json_files)} JSON files in {directory_path}")
            
            # Process each JSON file
            processed_files = 0
            for json_file in json_files:
                try:
                    # Load JSON file
                    with open(json_file, 'r', encoding='utf-8') as f:
                        table_def = json.load(f)
                    
                    # Extract table information
                    if 'table' not in table_def:
                        print(f"Warning: {json_file} does not contain a 'table' key, skipping")
                        continue
                        
                    table_info = table_def['table']
                    table_name = table_info.get('name')
                    columns = table_info.get('columns', [])
                    constraints = table_info.get('constraints', [])
                    
                    if not table_name or not columns:
                        print(f"Warning: {json_file} is missing required table information, skipping")
                        continue
                    
                    # Generate SQL DDL
                    sql_ddl = f"CREATE TABLE {table_name} (\n"
                    
                    # Add columns
                    column_defs = []
                    for col in columns:
                        col_name = col.get('name')
                        col_type = col.get('type')
                        
                        if not col_name or not col_type:
                            continue
                            
                        col_def = f"  {col_name} {col_type}"
                        
                        # Add nullable constraint
                        if 'nullable' in col and col['nullable'] is False:
                            col_def += " NOT NULL"
                            
                        # Add default value
                        if 'default' in col:
                            col_def += f" DEFAULT {col['default']}"
                        
                        column_defs.append(col_def)
                    
                    # Add primary key constraint
                    pk_columns = []
                    for constraint in constraints:
                        if constraint.get('type') == 'PRIMARY KEY':
                            pk_columns = constraint.get('columns', [])
                            break
                    
                    if pk_columns:
                        pk_constraint = f"  CONSTRAINT {table_name.split('.')[-1]}_pkey PRIMARY KEY({', '.join(pk_columns)})"
                        column_defs.append(pk_constraint)
                    
                    # Finalize SQL DDL
                    sql_ddl += ",\n".join(column_defs)
                    sql_ddl += "\n);"
                    
                    # Train with the DDL
                    self.vn.train(ddl=sql_ddl)
                    
                    # Create documentation from the column descriptions and labels
                    doc_text = f"Table: {table_name}\n\n"
                    doc_text += "Columns:\n"
                    
                    for col in columns:
                        col_name = col.get('name')
                        col_label = col.get('label', '')
                        col_desc = col.get('description', '')
                        col_type = col.get('type', '')
                        
                        doc_text += f"- {col_name} ({col_type}): {col_label} - {col_desc}\n"
                        
                        # Add enumerable values if present
                        if col.get('enumerable') and 'values' in col:
                            values_str = ", ".join([f'"{v}"' for v in col.get('values', [])])
                            doc_text += f"  Allowed values: {values_str}\n"
                    
                    # Train with the documentation
                    self.vn.train(documentation=doc_text)
                    
                    processed_files += 1
                    print(f"Processed {json_file.name}: Generated DDL and documentation")
                    
                except Exception as e:
                    print(f"Error processing {json_file}: {str(e)}")
                    continue
            
            print(f"Successfully processed {processed_files} JSON definition files")
            return processed_files
            
        except Exception as e:
            print(f"Error processing JSON directory: {str(e)}")
            return 0
    
    def get_table_schema_to_json(self, table_name, output_dir='training_data/dll_define'):
        """
        Retrieve schema information for a specified table from the database,
        convert it to the required JSON format, and save it to a file.
        
        Args:
            table_name (str): Name of the table to retrieve schema for (e.g., 'public.tablename')
            output_dir (str): Directory to save the JSON file
            
        Returns:
            str: Path to the created JSON file or None if failed
        """
        try:
            # Parse schema and table name
            if '.' in table_name:
                schema_name, table_name_only = table_name.split('.')
            else:
                schema_name = 'public'
                table_name_only = table_name
            
            # Query to get column information
            column_query = f"""
            SELECT 
                column_name, 
                data_type, 
                column_default, 
                is_nullable,
                col_description((table_schema || '.' || table_name)::regclass, ordinal_position) as description
            FROM 
                information_schema.columns
            WHERE 
                table_schema = '{schema_name}' OR table_name = '{table_name_only}'
            ORDER BY 
                ordinal_position
            """
            
            # Query to get primary key constraints
            pk_query = f"""
            SELECT
                tc.constraint_name,
                kcu.column_name
            FROM 
                information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                  ON tc.constraint_name = kcu.constraint_name
                  AND tc.table_schema = kcu.table_schema
            WHERE 
                tc.constraint_type = 'PRIMARY KEY'
                AND tc.table_schema = '{schema_name}'
                AND tc.table_name = '{table_name_only}'
            ORDER BY 
                kcu.ordinal_position
            """
            
            # Execute queries
            columns_result : pd.DataFrame = self.vn.run_sql(column_query)
            
            if not columns_result.empty:
                self.train_from_dataframe(columns_result, table_name)                        
            
            # Process primary key constraints
            '''
            if pk_result and len(pk_result) > 0:
                pk_columns = [row[1] for row in pk_result]  # column_name
                constraint_name = pk_result[0][0]  # constraint_name
                
                # Add primary key constraint
                table_def["table"]["constraints"].append({
                    "type": "PRIMARY KEY",
                    "name": constraint_name,
                    "columns": pk_columns
                })
                
                # Mark primary key columns
                for col in table_def["table"]["columns"]:
                    if col["name"] in pk_columns:
                        col["primary_key"] = True
            '''
            
            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)
            
            # Save to file
            output_file = os.path.join(output_dir, f"{table_name_only}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(table_def, f, indent=2, ensure_ascii=False)
            
            print(f"Successfully created schema JSON file: {output_file}")
            return output_file
            
        except Exception as e:
            print(f"Error retrieving schema for {table_name}: {str(e)}")
            return None
            
    def train_from_dataframe(self, df, table_name, output_dir='training_data/dll_define'):
        """
        Convert a pandas DataFrame to a JSON schema definition and save it for training.
        
        Args:
            df (pandas.DataFrame): DataFrame to convert to schema
            table_name (str): Name to use for the table in the schema
            output_dir (str): Directory to save the JSON file
            
        Returns:
            str: Path to the created JSON file or None if failed
        """
        try:
            if not isinstance(df, pd.DataFrame):
                print(f"Error: Input is not a pandas DataFrame")
                return None
                
            # Parse schema and table name
            if '.' in table_name:
                schema_name, table_name_only = table_name.split('.')
            else:
                schema_name = 'public'
                table_name_only = table_name
                
            # Create JSON structure
            table_def = {
                "table": {
                    "name": f"{schema_name}.{table_name_only}",
                    "columns": [],
                    "constraints": []
                }
            }
            
            # Process columns from DataFrame
            for col_name in df.columns:
                # Get column data type
                dtype = df[col_name].dtype
                sql_type = self._pandas_dtype_to_sql_type(dtype)
                
                # Create column definition
                column_def = {
                    "name": col_name,
                    "type": sql_type
                }
                
                # Check for nullable values
                if not df[col_name].isnull().any():
                    column_def["nullable"] = False
                    
                # Add description based on column name
                column_def["description"] = f"Column {col_name}"
                
                # Try to extract label from column name (if follows a pattern)
                label_match = re.search(r'([a-zA-Z_]+)\d*', col_name)
                if label_match:
                    column_def["label"] = label_match.group(1).replace('_', ' ').title()
                    
                # Check for enumerable values (categorical or limited unique values)
                if dtype == 'category' or (df[col_name].nunique() < 10 and df[col_name].nunique() > 1):
                    unique_values = df[col_name].dropna().unique().tolist()
                    if len(unique_values) > 0 and all(isinstance(x, (str, int, float)) for x in unique_values):
                        column_def["enumerable"] = True
                        column_def["values"] = [str(x) for x in unique_values[:10]]  # Limit to 10 values
                
                table_def["table"]["columns"].append(column_def)
            
            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)
            
            # Save to file
            output_file = os.path.join(output_dir, f"{table_name_only}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(table_def, f, indent=2, ensure_ascii=False)
            
            print(f"Successfully created schema JSON file from DataFrame: {output_file}")
            
            # Train with the schema
            self.train_from_dll_define_json(output_dir)
            
            return output_file
            
        except Exception as e:
            print(f"Error converting DataFrame to schema: {str(e)}")
            return None
            
    def _pandas_dtype_to_sql_type(self, dtype):
        """
        Convert pandas dtype to SQL data type.
        
        Args:
            dtype: pandas dtype
            
        Returns:
            str: SQL data type
        """
        dtype_str = str(dtype)
        
        if 'int' in dtype_str:
            return 'INTEGER'
        elif 'float' in dtype_str:
            return 'NUMERIC'
        elif 'datetime' in dtype_str:
            return 'TIMESTAMP'
        elif 'bool' in dtype_str:
            return 'BOOLEAN'
        elif 'category' in dtype_str:
            return 'TEXT'
        elif 'object' in dtype_str:
            return 'TEXT'
        else:
            return 'TEXT'  # Default to TEXT for unknown types

import os
import importlib
import inspect
from typing import Dict, Any, Optional, Type, Union

# Define model types
MODEL_TYPE_OPENAI = "openai"
MODEL_TYPE_DUCKDB_NSQL = "duckdb_nsql"

class ModelSwitcher:
    """Utility class to dynamically switch between different LLM backends for <PERSON>na.
    
    This allows users to easily switch between OpenAI/OpenRouter and local DuckDB-NSQL-7B
    without changing their code structure.
    """
    
    @staticmethod
    def get_model_class(model_type: str) -> Type:
        """Get the appropriate model class based on the specified type.
        
        Args:
            model_type: The type of model to use ("openai" or "duckdb_nsql")
            
        Returns:
            The model class to instantiate
            
        Raises:
            ImportError: If the requested model type module cannot be imported
            ValueError: If an invalid model type is specified
        """
        if model_type == MODEL_TYPE_OPENAI:
            try:
                from ..openai.openai_chat import OpenAI_Chat
                return OpenAI_Chat
            except ImportError:
                raise ImportError("Could not import OpenAI_Chat. Make sure openai is installed.")
        
        elif model_type == MODEL_TYPE_DUCKDB_NSQL:
            try:
                from ..duckdb_nsql.duckdb_nsql_chat import DuckDBNSQL_Chat
                return DuckDBNSQL_Chat
            except ImportError:
                raise ImportError("Could not import DuckDBNSQL_Chat. Make sure transformers and torch are installed.")
        
        else:
            raise ValueError(f"Invalid model type: {model_type}. Must be 'openai' or 'duckdb_nsql'.")
    
    @staticmethod
    def create_vanna_instance(model_type: str, vector_store_class: Type, model_config: Optional[Dict[str, Any]] = None, 
                              vector_config: Optional[Dict[str, Any]] = None) -> Any:
        """Create a Vanna instance with the specified model type and vector store.
        
        Args:
            model_type: The type of model to use ("openai" or "duckdb_nsql")
            vector_store_class: The vector store class to use (e.g., ChromaDB_VectorStore)
            model_config: Configuration for the model
            vector_config: Configuration for the vector store
            
        Returns:
            A custom Vanna instance combining the specified model and vector store
        """
        model_config = model_config or {}
        vector_config = vector_config or {}
        
        # Get the model class
        model_class = ModelSwitcher.get_model_class(model_type)
        
        # Create a dynamic class combining the vector store and model
        class CustomVanna(vector_store_class, model_class):
            def __init__(self, config=None):
                if config is None:
                    config = {}
                
                # Merge configurations
                config.update(model_config)
                config.update(vector_config)
                
                # Initialize parent classes
                vector_store_class.__init__(self, config=config)
                model_class.__init__(self, config=config)
        
        # Return an instance of the custom class
        return CustomVanna()
    
    @staticmethod
    def configure_openai(api_key: str, base_url: Optional[str] = None, model: Optional[str] = None) -> Dict[str, Any]:
        """Configure settings for OpenAI/OpenRouter.
        
        Args:
            api_key: The API key for OpenAI or OpenRouter
            base_url: Optional base URL for API (e.g., OpenRouter URL)
            model: Optional model name to use
            
        Returns:
            Configuration dictionary for OpenAI
        """
        from openai import OpenAI
        
        config = {}
        
        # Create OpenAI client
        if base_url:
            client = OpenAI(api_key=api_key, base_url=base_url)
        else:
            client = OpenAI(api_key=api_key)
        
        config["client"] = client
        
        if model:
            config["model"] = model
        
        return config
    
    @staticmethod
    def configure_duckdb_nsql(temperature: float = 0.1, max_new_tokens: int = 1024, 
                              device: Optional[str] = None) -> Dict[str, Any]:
        """Configure settings for DuckDB-NSQL-7B model.
        
        Args:
            temperature: Controls randomness in generation (lower = more deterministic)
            max_new_tokens: Maximum number of tokens to generate
            device: Device to run the model on ("cuda" or "cpu"), auto-detected if None
            
        Returns:
            Configuration dictionary for DuckDB-NSQL
        """
        config = {
            "temperature": temperature,
            "max_new_tokens": max_new_tokens
        }
        
        if device:
            config["device"] = device
        
        return config

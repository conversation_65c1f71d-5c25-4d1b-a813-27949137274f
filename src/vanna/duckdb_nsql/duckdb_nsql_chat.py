import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from ..base.base import VannaBase

class DuckDBNSQL_Singleton:
    _instance = None
    _model = None
    _tokenizer = None
    _model_name = "motherduckdb/DuckDB-NSQL-7B-v0.1"

    @classmethod
    def get_instance(cls, device=None, config=None):
        if cls._instance is None:
            cls._instance = cls(device=device, config=config)
        return cls._instance

    def __init__(self, device=None, config=None):
        if DuckDBNSQL_Singleton._instance is not None:
            raise Exception("This class is a singleton! Use get_instance() instead.")
        else:
            self.config = config or {}

            # Determine if we should use CUDA or CPU
            use_cuda = torch.cuda.is_available() and device != "cpu"
            self.device = "cuda" if use_cuda else "cpu"
            print(f"Loading DuckDB-NSQL-7B model on {self.device}...")

            try:
                # Load tokenizer and set padding token
                self._tokenizer = AutoTokenizer.from_pretrained(self._model_name)
                self._tokenizer.pad_token = self._tokenizer.eos_token

                # Configure model loading based on device
                if use_cuda:
                    # GPU configuration with quantization
                    try:
                        self._model = AutoModelForCausalLM.from_pretrained(
                            self._model_name,
                            torch_dtype=torch.float16,
                            device_map="auto",
                            load_in_4bit=True  # Use 4-bit quantization for memory efficiency
                        )
                    except ValueError as e:
                        if "requires `accelerate`" in str(e):
                            print("Warning: accelerate not installed. Falling back to standard GPU loading.")
                            self._model = AutoModelForCausalLM.from_pretrained(
                                self._model_name,
                                torch_dtype=torch.float16
                            ).to("cuda")
                        else:
                            raise
                else:
                    # CPU configuration
                    self._model = AutoModelForCausalLM.from_pretrained(
                        self._model_name,
                        torch_dtype=torch.float32,  # Use float32 for CPU compatibility
                        low_cpu_mem_usage=True      # Optimize for CPU memory usage
                    )
                print("DuckDB-NSQL-7B model loaded successfully.")
            except Exception as e:
                print(f"Error loading model: {str(e)}")
                raise


    def generate_response(self, prompt, max_new_tokens=1024, temperature=0.1):
        try:
            # Tokenize with attention mask explicitly set
            tokenized_input = self._tokenizer(
                prompt,
                return_tensors="pt",
                padding=True
            )
            input_ids = tokenized_input.input_ids
            attention_mask = tokenized_input.attention_mask

            # Move tensors to the correct device if using GPU
            if self.device == "cuda":
                input_ids = input_ids.to("cuda")
                attention_mask = attention_mask.to("cuda")

            # Generate with attention mask
            outputs = self._model.generate(
                input_ids,
                attention_mask=attention_mask,
                max_new_tokens=max_new_tokens,
                temperature=temperature,
                do_sample=True,
                top_p=0.95,
                pad_token_id=self._tokenizer.eos_token_id
            )
            response = self._tokenizer.decode(outputs[0], skip_special_tokens=True)
            # Extract the model's response (everything after the prompt)
            response = response[len(prompt):].strip()
            return response
        except Exception as e:
            print(f"Error generating response: {str(e)}")
            return f"Error generating response: {str(e)}"


class DuckDBNSQL_Chat(VannaBase):
    def __init__(self, config=None):
        VannaBase.__init__(self, config=config)

        # Default parameters - can be overridden using config
        self.temperature = 0.1
        self.max_new_tokens = 1024

        if "temperature" in config:
            self.temperature = config["temperature"]

        if "max_new_tokens" in config:
            self.max_new_tokens = config["max_new_tokens"]

        # Get the singleton instance of DuckDBNSQL_Singleton
        self.model = DuckDBNSQL_Singleton.get_instance(config=config)

    def system_message(self, message: str) -> any:
        return {"role": "system", "content": message}

    def user_message(self, message: str) -> any:
        return {"role": "user", "content": message}

    def assistant_message(self, message: str) -> any:
        return {"role": "assistant", "content": message}

    def generate_sql(self, question: str, **kwargs) -> str:
        # Use the super generate_sql
        sql = super().generate_sql(question, **kwargs)

        # Replace "\_" with "_"
        sql = sql.replace("\\_", "_")

        sql = sql.replace("\\", "")

        return self.extract_sql_query(sql)

    def submit_prompt(self, prompt, **kwargs) -> str:
        if prompt is None:
            raise Exception("Prompt is None")

        if len(prompt) == 0:
            raise Exception("Prompt is empty")

        # Convert the message format to a single string prompt
        formatted_prompt = ""
        for message in prompt:
            # Handle both string messages and dict messages with role/content
            if isinstance(message, dict) and "role" in message and "content" in message:
                role = message["role"]
                content = message["content"]

                if role == "system":
                    formatted_prompt += f"### System:\n{content}\n\n"
                elif role == "user":
                    formatted_prompt += f"### User:\n{content}\n\n"
                elif role == "assistant":
                    formatted_prompt += f"### Assistant:\n{content}\n\n"
            else:
                # If it's just a string, treat it as user message
                formatted_prompt += f"### User:\n{message}\n\n"

        # Add the final assistant prompt
        formatted_prompt += "### Assistant:\n"

        # Count tokens (approximation)
        num_tokens = len(formatted_prompt.split())
        print(f"Using DuckDB-NSQL-7B model for approximately {num_tokens} tokens")

        # Generate response using the singleton model
        response = self.generate_response(formatted_prompt)

        return response

    def generate_response(self, prompt, **kwargs):
        """Generate a response using the DuckDB-NSQL model.

        Args:
            prompt: The text prompt to generate a response for

        Returns:
            The generated response text
        """
        try:
            # Get parameters from kwargs or use defaults
            max_new_tokens = kwargs.get("max_new_tokens", self.max_new_tokens)
            temperature = kwargs.get("temperature", self.temperature)

            # Tokenize with attention mask explicitly set
            tokenized_input = self.model._tokenizer(
                prompt,
                return_tensors="pt",
                padding=True
            )
            input_ids = tokenized_input.input_ids
            attention_mask = tokenized_input.attention_mask

            # Move tensors to the correct device if using GPU
            if self.model.device == "cuda":
                input_ids = input_ids.to("cuda")
                attention_mask = attention_mask.to("cuda")

            # Generate with attention mask
            generated_ids = self.model._model.generate(
                input_ids,
                attention_mask=attention_mask,
                max_new_tokens=max_new_tokens,
                do_sample=True,
                temperature=temperature,
                top_p=0.95,
                pad_token_id=self.model._tokenizer.eos_token_id
            )

            # Decode the response
            response = self.model._tokenizer.decode(generated_ids[0], skip_special_tokens=True)

            # Extract just the generated part (after the prompt)
            generated_part = response[len(prompt):].strip()
            return generated_part

        except Exception as e:
            print(f"Error generating response: {str(e)}")
            return f"Error generating response: {str(e)}"

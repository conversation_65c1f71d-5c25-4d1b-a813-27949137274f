import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

#model_name = "motherduckdb/DuckDB-NSQL-7B-v0.1"
model_name = "google/gemma-3n-E4B-it-litert-preview"
print(f"Loading {model_name} on CPU...")

# Load tokenizer
tokenizer = AutoTokenizer.from_pretrained(model_name)

# Set pad_token to eos_token to fix the padding issue
tokenizer.pad_token = tokenizer.eos_token

# Load model for CPU without requiring accelerate
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    torch_dtype=torch.float32,  # Use float32 for CPU compatibility
    device_map=None,           # Don't use device_map on CPU
    low_cpu_mem_usage=True     # Optimize for CPU memory usage
)

text = """### Instruction:
You are a PostgreSQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions.

### Input:
### System:{
  "table": {
    "name": "public.prjtmproj",
    "schema": "public",
    "description": "Project protocol management table",
    "label": "計畫基本資料表",
    "columns": [
      {
        "name": "iprotocol24_id",
        "type": "TEXT",
        "nullable": false,
        "description": "Protocol ID",
        "label": "計畫編號編號",
        "primary_key": true
      },
      {
        "name": "protocol24_id",
        "type": "TEXT",
        "description": "Protocol ID",
        "label": "計畫編號"
      },
      {
        "name": "protabbrnm80_tx",
        "type": "TEXT",
        "description": "Protocol abbreviated name",
        "label": "計畫縮寫名稱"
      },
      {
        "name": "protnm240_tx",
        "type": "TEXT",
        "description": "Protocol name",
        "label": "計畫名稱"
      },
      {
        "name": "byear8_nm",
        "type": "NUMERIC",
        "storage": "MAIN",
        "description": "Base year",
        "label": "基準年份"
      },
      {
        "name": "totalcase8_nm",
        "type": "NUMERIC",
        "storage": "MAIN",
        "description": "Total cases",
        "label": "總案例數"
      },
      {
        "name": "yearcase8_nm",
        "type": "NUMERIC",
        "storage": "MAIN",
        "description": "Cases per year",
        "label": "年度案例數"
      },
      {
        "name": "totalyear8_nm",
        "type": "NUMERIC",
        "storage": "MAIN",
        "description": "Total years",
        "label": "總年數"
      },
      {
        "name": "commit120_tx",
        "type": "TEXT",
        "description": "Commitment",
        "label": "承諾內容"
      },
      {
        "name": "treatment120_tx",
        "type": "TEXT",
        "description": "Treatment",
        "label": "治療方案"
      },
      {
        "name": "randomize8_fl",
        "type": "TEXT",
        "description": "Randomize flag",
        "label": "隨機化標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "randomize8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "randomize8_fl = 'N'"}
        ]
      },
      {
        "name": "testtypecd24_tx",
        "type": "TEXT",
        "description": "Test type code",
        "label": "測試類型代碼"
      },
      {
        "name": "studydesign48_tx",
        "type": "TEXT",
        "description": "Study design",
        "label": "研究設計"
      },
      {
        "name": "strat8_fl",
        "type": "TEXT",
        "description": "Stratification flag",
        "label": "分層標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "strat8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "strat8_fl = 'N'"}
        ]
      },
      {
        "name": "stagecd16_tx",
        "type": "TEXT",
        "description": "Stage code",
        "label": "階段代碼"
      },
      {
        "name": "projkey24_tx",
        "type": "TEXT",
        "description": "Project key",
        "label": "項目密鑰"
      },
      {
        "name": "meda32_tx",
        "type": "TEXT",
        "description": "Medicine A",
        "label": "藥物A"
      },
      {
        "name": "medb32_tx",
        "type": "TEXT",
        "description": "Medicine B",
        "label": "藥物B"
      },
      {
        "name": "medc32_tx",
        "type": "TEXT",
        "description": "Medicine C",
        "label": "藥物C"
      },
      {
        "name": "medd32_tx",
        "type": "TEXT",
        "description": "Medicine D",
        "label": "藥物D"
      },
      {
        "name": "mede32_tx",
        "type": "TEXT",
        "description": "Medicine E",
        "label": "藥物E"
      },
      {
        "name": "medf32_tx",
        "type": "TEXT",
        "description": "Medicine F",
        "label": "藥物F"
      },
      {
        "name": "medg32_tx",
        "type": "TEXT",
        "description": "Medicine G",
        "label": "藥物G"
      },
      {
        "name": "medh32_tx",
        "type": "TEXT",
        "description": "Medicine H",
        "label": "藥物H"
      },
      {
        "name": "medi32_tx",
        "type": "TEXT",
        "description": "Medicine I",
        "label": "藥物I"
      },
      {
        "name": "medj32_tx",
        "type": "TEXT",
        "description": "Medicine J",
        "label": "藥物J"
      },
      {
        "name": "bmonth8_tx",
        "type": "TEXT",
        "description": "Base month",
        "label": "基準月份"
      },
      {
        "name": "registercd8_cd",
        "type": "TEXT",
        "description": "Register code",
        "label": "註冊代碼"
      },
      {
        "name": "idic24_id",
        "type": "TEXT",
        "description": "IDIC ID",
        "label": "IDIC編號"
      },
      {
        "name": "dmstaff24_id",
        "type": "TEXT",
        "description": "Data management staff ID",
        "label": "數據管理人員編號"
      },
      {
        "name": "iversion24_ft",
        "type": "TEXT",
        "description": "Version ID",
        "label": "版本編號"
      },
      {
        "name": "mbase24_dt",
        "type": "TIMESTAMP WITHOUT TIME ZONE",
        "storage": "PLAIN",
        "description": "Base date/time",
        "label": "基準日期時間"
      },
      {
        "name": "suspend24_dt",
        "type": "TIMESTAMP WITHOUT TIME ZONE",
        "storage": "PLAIN",
        "description": "Suspend date/time",
        "label": "暂停日期時間"
      },
      {
        "name": "stop24_dt",
        "type": "TIMESTAMP WITHOUT TIME ZONE",
        "storage": "PLAIN",
        "description": "Stop date/time",
        "label": "停止日期時間"
      },
      {
        "name": "qc8_fl",
        "type": "TEXT",
        "description": "QC flag",
        "label": "質量控制標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "qc8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "qc8_fl = 'N'"}
        ]
      },
      {
        "name": "compare8_fl",
        "type": "TEXT",
        "description": "Compare flag",
        "label": "比較標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "compare8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "compare8_fl = 'N'"}
        ]
      },
      {
        "name": "suspend8_fl",
        "type": "TEXT",
        "description": "Suspend flag",
        "label": "暂停標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "suspend8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "suspend8_fl = 'N'"}
        ]
      },
      {
        "name": "stop8_fl",
        "type": "TEXT",
        "description": "Stop flag",
        "label": "停止標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "stop8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "stop8_fl = 'N'"}
        ]
      },
      {
        "name": "dblock8_fl",
        "type": "TEXT",
        "description": "Database lock flag",
        "label": "數据库鎖定標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "dblock8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "dblock8_fl = 'N'"}
        ]
      },
      {
        "name": "dblock24_dt",
        "type": "TIMESTAMP WITHOUT TIME ZONE",
        "storage": "PLAIN",
        "description": "Database lock date/time",
        "label": "數据库鎖定日期時間"
      },
      {
        "name": "syslock24_dt",
        "type": "TIMESTAMP WITHOUT TIME ZONE",
        "storage": "PLAIN",
        "description": "System lock date/time",
        "label": "系統鎖定日期時間"
      },
      {
        "name": "dbunlock24_dt",
        "type": "TIMESTAMP WITHOUT TIME ZONE",
        "storage": "PLAIN",
        "description": "Database unlock date/time",
        "label": "數据库解鎖日期時間"
      },
      {
        "name": "sysunlock24_dt",
        "type": "TIMESTAMP WITHOUT TIME ZONE",
        "storage": "PLAIN",
        "description": "System unlock date/time",
        "label": "系統解鎖日期時間"
      },
      {
        "name": "iblibdcone24_id",
        "type": "TEXT",
        "description": "Library DCone ID",
        "label": "庫文件DCone編號"
      },
      {
        "name": "imediversion24_ft",
        "type": "TEXT",
        "description": "Media version ID",
        "label": "媒體版本編號"
      },
      {
        "name": "prostatus8_tx",
        "type": "TEXT",
        "description": "Protocol status",
        "label": "協議狀態"
      },
      {
        "name": "igog24_id",
        "type": "TEXT",
        "description": "GOG ID",
        "label": "GOG編號"
      },
      {
        "name": "prjclose24_dt",
        "type": "TIMESTAMP WITHOUT TIME ZONE",
        "storage": "PLAIN",
        "description": "Project close date/time",
        "label": "項目關閉日期時間"
      },
      {
        "name": "prjstart24_dt",
        "type": "TIMESTAMP WITHOUT TIME ZONE",
        "storage": "PLAIN",
        "description": "Project start date/time",
        "label": "項目開始日期時間"
      },
      {
        "name": "irtog24_id",
        "type": "TEXT",
        "description": "RTOG ID",
        "label": "RTOG編號"
      },
      {
        "name": "dbdecrypt8_fl",
        "type": "TEXT",
        "description": "Database decrypt flag",
        "label": "數据库解密標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "dbdecrypt8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "dbdecrypt8_fl = 'N'"}
        ]
      },
      {
        "name": "prodesign128_tx",
        "type": "TEXT",
        "description": "Protocol design",
        "label": "協議設計"
      },
      {
        "name": "sche8_fl",
        "type": "TEXT",
        "description": "Schedule flag",
        "label": "排程標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "sche8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "sche8_fl = 'N'"}
        ]
      },
      {
        "name": "iguid32_ft",
        "type": "TEXT",
        "default": "gen_random_uuid()",
        "description": "UUID field",
        "label": "全局唯一標識符"
      },
      {
        "name": "ptype8_cd",
        "type": "TEXT",
        "description": "Page type code",
        "label": "頁面類型代碼",
        "enumerable": true,
        "values": [
          {"code": "P", "decode": "Paper Flow", "label": "紙本流程", "sql_example": "ptype8_cd = 'P'"},
          {"code": "E", "decode": "EDC Flow", "label": "EDC流程", "sql_example": "ptype8_cd = 'E'"},
          {"code": "S", "decode": "e-SOURCE", "label": "電子源", "sql_example": "ptype8_cd = 'S'"},
          {"code": "C", "decode": "CDISC", "label": "CDISC", "sql_example": "ptype8_cd = 'C'"}
        ]
      },
      {
        "name": "trem8_fl",
        "type": "TEXT",
        "description": "Treatment flag",
        "label": "治療標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "trem8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "trem8_fl = 'N'"}
        ]
      },
      {
        "name": "bathmailqcerr8_fl",
        "type": "TEXT",
        "description": "Batch mail QC error flag",
        "label": "批量郵件QC錯誤標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "bathmailqcerr8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "bathmailqcerr8_fl = 'N'"}
        ]
      },
      {
        "name": "bathmailqcsta8_fl",
        "type": "TEXT",
        "description": "Batch mail QC status flag",
        "label": "批量郵件QC狀態標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "bathmailqcsta8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "bathmailqcsta8_fl = 'N'"}
        ]
      },
      {
        "name": "icomp24_id",
        "type": "TEXT",
        "description": "Company ID",
        "label": "公司編號"
      },
      {
        "name": "dblocktype8_id",
        "type": "TEXT",
        "description": "Database lock type ID",
        "label": "數据库鎖定類型編號"
      },
      {
        "name": "esrciprotocol24_id",
        "type": "TEXT",
        "description": "ESRC protocol ID",
        "label": "ESRC協議編號"
      },
      {
        "name": "prandom8_fl",
        "type": "TEXT",
        "description": "Protocol random flag",
        "label": "協議隨機標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "prandom8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "prandom8_fl = 'N'"}
        ]
      },
      {
        "name": "pseed16_nm",
        "type": "NUMERIC",
        "storage": "MAIN",
        "description": "Protocol seed number",
        "label": "協議種子數"
      },
      {
        "name": "monitor8_fl",
        "type": "TEXT",
        "description": "Monitor flag",
        "label": "監控標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "monitor8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "monitor8_fl = 'N'"}
        ]
      },
      {
        "name": "cdiscaecm8_fl",
        "type": "TEXT",
        "description": "CDISC AECM flag",
        "label": "CDISC AECM標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "cdiscaecm8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "cdiscaecm8_fl = 'N'"}
        ]
      },
      {
        "name": "iprotocol32_ft",
        "type": "TEXT",
        "default": "gen_random_uuid()",
        "description": "Protocol UUID",
        "label": "協議UUID"
      },
      {
        "name": "cohort8_fl",
        "type": "TEXT",
        "description": "Cohort flag",
        "label": "隊列標誌"
      },
      {
        "name": "screeningcd8_cd",
        "type": "TEXT",
        "description": "Screening code",
        "label": "篩選代碼"
      },
      {
        "name": "randomctl8_fl",
        "type": "TEXT",
        "description": "Random control flag",
        "label": "隨機控制標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "randomctl8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "randomctl8_fl = 'N'"}
        ]
      },
      {
        "name": "treatment8_fl",
        "type": "TEXT",
        "description": "Treatment flag",
        "label": "治療標誌",
        "enumerable": true,
        "values": [
          {"code": "Y", "decode": "Yes", "label": "是", "sql_example": "treatment8_fl = 'Y'"},
          {"code": "N", "decode": "No", "label": "否", "sql_example": "treatment8_fl = 'N'"}
        ]
      }
    ],
    "constraints": [
      {
        "type": "PRIMARY KEY",
        "name": "prjtmproj_pkey",
        "columns": [
          "iprotocol24_id"
        ]
      }
    ]
  }
}
### Question:
有多少EDC流程計畫?

### Response (use duckdb shorthand if possible):"""

tokenized_input = tokenizer(text, return_tensors="pt", padding=True)
input_ids = tokenized_input.input_ids
attention_mask = tokenized_input.attention_mask

generated_ids = model.generate(
    input_ids, 
    attention_mask=attention_mask,
    max_new_tokens=500,  # Use max_new_tokens instead of max_length
    do_sample=True,
    temperature=0.1,
    top_p=0.95
)

response = tokenizer.decode(generated_ids[0], skip_special_tokens=True)
print(response)

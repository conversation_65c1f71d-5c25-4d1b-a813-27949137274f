# PDF Vector Store with Local Embeddings

This implementation provides a solution for storing, indexing, and querying PDF documents using local embeddings and a vector database.

## Features

- Load and process PDF documents
- Generate embeddings using Sentence Transformers (local model)
- Store embeddings and metadata in ChromaDB
- Query documents using natural language
- Retrieve relevant context and sources for each query
- Support for adding metadata to documents

## Requirements

- Python 3.8+
- Required packages (install with `uv`):

```bash
uv pip install sentence-transformers pypdf2 chromadb python-dotenv openai
```

## Usage

### Basic Usage

1. Add a PDF and query it:
   ```bash
   python local_pdf_query.py path/to/your/document.pdf "Your question about the document"
   ```

2. Query an already added PDF:
   ```bash
   python local_pdf_query.py path/to/your/document.pdf "Your question about the document"
   ```

### OpenAI Integration (Optional)

If you want to use OpenAI for generating responses (instead of just returning relevant chunks), set your OpenAI API key:

```bash
export OPENAI_API_KEY="your-api-key"
```

Or create a `.env` file with:
```
OPENAI_API_KEY=your-api-key
```

## How It Works

1. **Document Loading**: PDFs are loaded using PyPDF2
2. **Text Chunking**: Documents are split into manageable chunks with overlap
3. **Embedding Generation**: Sentence Transformers converts text chunks into vector representations
4. **Vector Storage**: Embeddings and metadata are stored in ChromaDB
5. **Querying**: Natural language queries are converted to embeddings and semantically similar chunks are retrieved

## Implementation Details

### PDFVectorStore Class

The main class that handles all operations:

- `__init__`: Initialize the store with ChromaDB and Sentence Transformers
- `_get_embedding`: Generate embeddings for text using Sentence Transformers
- `_extract_text_from_pdf`: Extract text from PDF files
- `_chunk_text`: Split text into chunks with overlap
- `add_pdf`: Add a PDF document to the vector store
- `query`: Query the vector store with natural language
- `get_document_count`: Get the number of documents in the collection

### Customization

You can customize the following parameters when initializing the PDF store:

- `persist_dir`: Directory to persist the vector store
- `collection_name`: Name of the ChromaDB collection
- `model_name`: Name of the Sentence Transformers model to use

## Troubleshooting

- **PDF Loading Errors**: Make sure the PDF is not corrupted and is accessible
- **Memory Issues**: For large PDFs, consider reducing the chunk size or processing files individually
- **Embedding Model Errors**: If you encounter issues with the default model, try using a smaller model like "paraphrase-MiniLM-L3-v2"

## Alternative Implementations

This repository also includes other implementations that you can try:

1. `openai_pdf_query.py`: Uses OpenAI embeddings instead of local embeddings
2. `simple_llamaindex_pdf.py`: Uses LlamaIndex for document processing and indexing
3. `modular_llamaindex_pdf.py`: Uses the modular approach of LlamaIndex
4. `llamaindex_pdf_store_openai.py`: A more comprehensive implementation using LlamaIndex

## Performance Considerations

- The local embedding model requires more memory but eliminates the need for API calls
- For large document collections, consider using a more powerful embedding model
- ChromaDB provides good performance for small to medium-sized document collections

## Future Improvements

- Add support for batch processing of multiple PDFs
- Implement metadata filtering for more targeted queries
- Add support for other document formats (DOCX, TXT, etc.)
- Implement document update and deletion functionality

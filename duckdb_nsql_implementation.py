import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from abc import ABC, abstractmethod

# Singleton class for the DuckDB-NSQL-7B model
class DuckDBNSQL_Singleton:
    _instance = None
    _model = None
    _tokenizer = None
    _model_name = "motherduckdb/DuckDB-NSQL-7B-v0.1"
    
    @classmethod
    def get_instance(cls, device=None, config=None):
        if cls._instance is None:
            cls._instance = cls(device=device, config=config)
        return cls._instance
    
    def __init__(self, device=None, config=None):
        if DuckDBNSQL_Singleton._instance is not None:
            raise Exception("This class is a singleton! Use get_instance() instead.")
        else:
            self.config = config or {}
            
            # Determine if we should use CUDA or CPU
            use_cuda = torch.cuda.is_available() and device != "cpu"
            self.device = "cuda" if use_cuda else "cpu"
            print(f"Loading DuckDB-NSQL-7B model on {self.device}...")
            
            try:
                # Load tokenizer and set padding token
                self._tokenizer = AutoTokenizer.from_pretrained(self._model_name)
                self._tokenizer.pad_token = self._tokenizer.eos_token
                
                # Configure model loading based on device
                if use_cuda:
                    # GPU configuration with quantization
                    try:
                        self._model = AutoModelForCausalLM.from_pretrained(
                            self._model_name,
                            torch_dtype=torch.float16,
                            device_map="auto",
                            load_in_4bit=True  # Use 4-bit quantization for memory efficiency
                        )
                    except ValueError as e:
                        if "requires `accelerate`" in str(e):
                            print("Warning: accelerate not installed. Falling back to standard GPU loading.")
                            self._model = AutoModelForCausalLM.from_pretrained(
                                self._model_name,
                                torch_dtype=torch.float16
                            ).to("cuda")
                        else:
                            raise
                else:
                    # CPU configuration
                    self._model = AutoModelForCausalLM.from_pretrained(
                        self._model_name,
                        torch_dtype=torch.float32,  # Use float32 for CPU compatibility
                        low_cpu_mem_usage=True      # Optimize for CPU memory usage
                    )
                print("DuckDB-NSQL-7B model loaded successfully.")
            except Exception as e:
                print(f"Error loading model: {str(e)}")
                raise
    
    def generate_response(self, prompt, max_new_tokens=1024, temperature=0.1):
        try:
            # Tokenize with attention mask explicitly set
            tokenized_input = self._tokenizer(prompt, return_tensors="pt", padding=True)
            input_ids = tokenized_input.input_ids
            attention_mask = tokenized_input.attention_mask
            
            # Move tensors to the correct device if using GPU
            if self.device == "cuda":
                input_ids = input_ids.to("cuda")
                attention_mask = attention_mask.to("cuda")
            
            # Generate with attention mask
            generated_ids = self._model.generate(
                input_ids,
                attention_mask=attention_mask,
                max_new_tokens=max_new_tokens,
                do_sample=True,
                temperature=temperature,
                top_p=0.95,
                pad_token_id=self._tokenizer.eos_token_id
            )
            
            # Decode the response
            response = self._tokenizer.decode(generated_ids[0], skip_special_tokens=True)
            
            # Extract just the generated part (after the prompt)
            generated_part = response[len(prompt):].strip()
            return generated_part
            
        except Exception as e:
            print(f"Error generating response: {str(e)}")
            return f"Error generating response: {str(e)}"

# Simple example of using the singleton
def test_duckdb_nsql_singleton():
    # Get the singleton instance
    model = DuckDBNSQL_Singleton.get_instance(device="cpu")
    
    # Define a test prompt
    prompt = """
    ### Instruction:
    Your task is to generate valid duckdb SQL to answer the following question, given a duckdb database schema.

    ### Input:
    Here is the database schema that the SQL query will run on:
    CREATE TABLE taxi (
        VendorID bigint,
        tpep_pickup_datetime timestamp,
        tpep_dropoff_datetime timestamp,
        passenger_count double,
        trip_distance double,
        fare_amount double,
        extra double,
        tip_amount double,
        tolls_amount double,
        improvement_surcharge double,
        total_amount double,
    );

    ### Question:
    get all columns ending with _amount from taxi table

    ### Response (use duckdb shorthand if possible):
    """
    
    # Generate response
    response = model.generate_response(prompt)
    print(f"Generated SQL: {response}")

# Run the test if this script is executed directly
if __name__ == "__main__":
    test_duckdb_nsql_singleton()

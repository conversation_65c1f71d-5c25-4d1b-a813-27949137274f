import os
import sys
import time
import json
from typing import List, Dict, Any

# Import the model switcher utility
from src.vanna.utils.model_switcher import ModelSwitcher, MODEL_TYPE_OPENAI, MODEL_TYPE_DUCKDB_NSQL
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction

# Create a singleton class for the embedding function
class EmbeddingFunctionSingleton:
    _instance = None
    _model_path = './embedding_model_cache'
    _model_name = "intfloat/multilingual-e5-base"
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            print("Loading embedding model for the first time...")
            cls._instance = SentenceTransformerEmbeddingFunction(model_name=cls._model_name, cache_folder=cls._model_path)
            print("Embedding model loaded successfully.")
        return cls._instance

# Sample test questions for benchmarking
TEST_QUESTIONS = [
    "What are the top 5 users by order count?",
    "Show me the average order amount by product category",
    "Which products have never been ordered?",
    "List the total revenue by month in 2023",
    "Find customers who have spent more than $1000 in total"
]

def create_vanna_instance(model_type):
    """Create a Vanna instance with the specified model type."""
    # Get the embedding function instance
    ef = EmbeddingFunctionSingleton.get_instance()
    
    # Configure vector store settings
    vector_config = {
        "embedding_function": ef,
        "n_results": 5,
        "path": "./chromadb_store"
    }
    
    if model_type == MODEL_TYPE_OPENAI:
        # Configure OpenAI/OpenRouter
        model_config = ModelSwitcher.configure_openai(
            api_key='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',
            base_url="https://openrouter.ai/api/v1",
            model="gpt-3.5-turbo"
        )
    else:  # MODEL_TYPE_DUCKDB_NSQL
        # Configure DuckDB-NSQL
        model_config = ModelSwitcher.configure_duckdb_nsql(
            temperature=0.1,
            max_new_tokens=1024
        )
    
    # Create and return the Vanna instance
    return ModelSwitcher.create_vanna_instance(
        model_type=model_type,
        vector_store_class=ChromaDB_VectorStore,
        model_config=model_config,
        vector_config=vector_config
    )

def benchmark_model(model_type: str, questions: List[str]) -> Dict[str, Any]:
    """Benchmark a model on a set of questions.
    
    Args:
        model_type: The type of model to benchmark
        questions: List of questions to test
        
    Returns:
        Dictionary with benchmark results
    """
    print(f"\nBenchmarking {model_type}...")
    
    # Create Vanna instance
    vn = create_vanna_instance(model_type)
    
    # Initialize results
    results = {
        "model_type": model_type,
        "questions": [],
        "total_time": 0,
        "average_time": 0,
        "success_rate": 0
    }
    
    # Test each question
    successes = 0
    for i, question in enumerate(questions):
        print(f"\nQuestion {i+1}/{len(questions)}: {question}")
        
        question_result = {
            "question": question,
            "success": False,
            "time_taken": 0,
            "sql": None,
            "error": None
        }
        
        # Generate SQL
        start_time = time.time()
        try:
            sql = vn.generate_sql(question)
            success = True
            successes += 1
            question_result["sql"] = sql
            question_result["success"] = True
            print(f"Generated SQL: {sql}")
        except Exception as e:
            error_msg = str(e)
            question_result["error"] = error_msg
            success = False
            print(f"Error: {error_msg}")
        
        end_time = time.time()
        time_taken = end_time - start_time
        
        # Update results
        question_result["time_taken"] = time_taken
        results["questions"].append(question_result)
        results["total_time"] += time_taken
        
        print(f"Time taken: {time_taken:.2f} seconds")
    
    # Calculate summary statistics
    results["average_time"] = results["total_time"] / len(questions)
    results["success_rate"] = successes / len(questions)
    
    print(f"\nSummary for {model_type}:")
    print(f"Total time: {results['total_time']:.2f} seconds")
    print(f"Average time per question: {results['average_time']:.2f} seconds")
    print(f"Success rate: {results['success_rate'] * 100:.1f}%")
    
    return results

def main():
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Benchmark different LLM models for Vanna")
    parser.add_argument("--models", nargs="+", choices=["openai", "duckdb_nsql", "all"], default=["all"],
                        help="Models to benchmark (openai, duckdb_nsql, or all)")
    parser.add_argument("--output", type=str, default="benchmark_results.json",
                        help="Output file for benchmark results")
    parser.add_argument("--questions", type=str, help="Path to JSON file with custom test questions")
    args = parser.parse_args()
    
    # Load custom questions if provided
    questions = TEST_QUESTIONS
    if args.questions:
        try:
            with open(args.questions, 'r') as f:
                custom_questions = json.load(f)
                if isinstance(custom_questions, list) and all(isinstance(q, str) for q in custom_questions):
                    questions = custom_questions
                    print(f"Loaded {len(questions)} custom questions from {args.questions}")
                else:
                    print(f"Invalid format in {args.questions}. Using default questions.")
        except Exception as e:
            print(f"Error loading questions from {args.questions}: {str(e)}")
            print("Using default questions.")
    
    # Determine which models to benchmark
    models_to_benchmark = []
    if "all" in args.models:
        models_to_benchmark = [MODEL_TYPE_OPENAI, MODEL_TYPE_DUCKDB_NSQL]
    else:
        models_to_benchmark = args.models
    
    # Run benchmarks
    results = []
    for model_type in models_to_benchmark:
        model_results = benchmark_model(model_type, questions)
        results.append(model_results)
    
    # Save results to file
    with open(args.output, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nBenchmark results saved to {args.output}")
    
    # Print comparison if both models were benchmarked
    if len(results) > 1:
        print("\nModel Comparison:")
        print("-" * 50)
        print(f"{'Model':<15} {'Avg Time':<15} {'Success Rate':<15}")
        print("-" * 50)
        for result in results:
            model_name = result["model_type"]
            avg_time = f"{result['average_time']:.2f}s"
            success_rate = f"{result['success_rate'] * 100:.1f}%"
            print(f"{model_name:<15} {avg_time:<15} {success_rate:<15}")

if __name__ == "__main__":
    main()

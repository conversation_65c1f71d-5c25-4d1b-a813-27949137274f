from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
import numpy as np
import os
from pathlib import Path

# Test with Traditional Chinese text
traditional_chinese_texts = [
    "這是一個測試句子",  # "This is a test sentence"
    "數據庫查詢優化",    # "Database query optimization"
    "自然語言處理技術",   # "Natural language processing technology"
    "今天天氣很好",       # "The weather is nice today"
    "機器學習模型訓練"     # "Machine learning model training"
]

print("\n=== Finding Model Download Location ===\n")

try:
    # Import the SentenceTransformer library
    from sentence_transformers import SentenceTransformer
    
    # This will trigger the download if the model doesn't exist
    print("Loading model (this will download it if not already present)...")
    model = SentenceTransformer('intfloat/multilingual-e5-base')
    print("Model loaded successfully!")
    
    # Check common locations for the model files
    print("\nChecking common model storage locations:")
    
    # 1. Hugging Face cache directory
    hf_cache = os.path.expanduser('~/.cache/huggingface/hub')
    hf_model_dir = os.path.join(hf_cache, 'models--intfloat--multilingual-e5-base')
    print(f"1. Hugging Face cache: {hf_model_dir}")
    print(f"   Exists: {os.path.exists(hf_model_dir)}")
    
    # 2. Torch cache directory
    torch_cache = os.path.expanduser('~/.cache/torch/sentence_transformers')
    torch_model_dir = os.path.join(torch_cache, 'intfloat_multilingual-e5-base')
    print(f"2. Torch cache: {torch_model_dir}")
    print(f"   Exists: {os.path.exists(torch_model_dir)}")
    
    # 3. Current working directory
    cwd_model = os.path.join(os.getcwd(), 'intfloat_multilingual-e5-base')
    print(f"3. Current directory: {cwd_model}")
    print(f"   Exists: {os.path.exists(cwd_model)}")
    
    # Find the actual location by checking where files exist
    model_location = None
    
    if os.path.exists(hf_model_dir):
        model_location = hf_model_dir
        print("\nModel found in Hugging Face cache!")
        
        # Show snapshot directory if it exists
        snapshots_dir = os.path.join(hf_model_dir, 'snapshots')
        if os.path.exists(snapshots_dir):
            snapshots = os.listdir(snapshots_dir)
            if snapshots:
                snapshot_dir = os.path.join(snapshots_dir, snapshots[0])
                print(f"Latest snapshot: {snapshot_dir}")
                
                # Calculate size
                total_size = sum(
                    os.path.getsize(os.path.join(root, file))
                    for root, _, files in os.walk(snapshot_dir)
                    for file in files
                )
                print(f"Total size: {total_size / (1024 * 1024):.2f} MB")
                
                # List files
                print("\nModel files:")
                for file in sorted(os.listdir(snapshot_dir))[:10]:
                    file_path = os.path.join(snapshot_dir, file)
                    if os.path.isfile(file_path):
                        size = os.path.getsize(file_path) / (1024 * 1024)
                        print(f"  - {file} ({size:.2f} MB)")
    
    elif os.path.exists(torch_model_dir):
        model_location = torch_model_dir
        print("\nModel found in Torch cache!")
        
        # Calculate size
        total_size = sum(
            os.path.getsize(os.path.join(root, file))
            for root, _, files in os.walk(torch_model_dir)
            for file in files
        )
        print(f"Total size: {total_size / (1024 * 1024):.2f} MB")
        
        # List files
        print("\nModel files:")
        for file in sorted(os.listdir(torch_model_dir))[:10]:
            file_path = os.path.join(torch_model_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path) / (1024 * 1024)
                print(f"  - {file} ({size:.2f} MB)")
    
    elif os.path.exists(cwd_model):
        model_location = cwd_model
        print("\nModel found in current directory!")
    
    else:
        print("\nModel not found in common locations. It might be in a different directory.")
        
        # Try to find by looking at model object
        print("\nTrying to get model info from the loaded model object:")
        print(f"Model type: {type(model)}")
        print(f"Model modules: {dir(model)[:10]}...")
        
        # Try to find by searching common locations
        print("\nSearching for model files in common cache directories...")
        for root, dirs, files in os.walk(os.path.expanduser('~/.cache')):
            if 'multilingual-e5-base' in root:
                print(f"Found potential model directory: {root}")
                break

    # Now test the embedding function with Traditional Chinese text
    print("\n=== Testing Embeddings with Traditional Chinese ===\n")
    
    # Use a multilingual model that's good for Traditional Chinese
    ef = SentenceTransformerEmbeddingFunction(model_name="intfloat/multilingual-e5-base")
    
    # Generate embeddings
    embeddings = ef(traditional_chinese_texts)
    
    # Show the results
    for text, embedding in zip(traditional_chinese_texts, embeddings):
        print(f"Text: {text}")
        print(f"Embedding shape: {np.array(embedding).shape}")
        print(f"First 5 dimensions: {np.array(embedding)[:5]}")
        print("-" * 50)
        
    # Test similarity with a query
    query = "數據庫"  # "database"
    query_embedding = ef([query])[0]
    
    print(f"\nQuery: {query}")
    print("Similarity with each text:")
    
    from sklearn.metrics.pairwise import cosine_similarity
    query_embedding_reshaped = np.array(query_embedding).reshape(1, -1)
    
    for i, (text, emb) in enumerate(zip(traditional_chinese_texts, embeddings)):
        emb_reshaped = np.array(emb).reshape(1, -1)
        similarity = cosine_similarity(query_embedding_reshaped, emb_reshaped)[0][0]
        print(f"  {i+1}. {text}: {similarity:.4f}")

except Exception as e:
    print(f"Error: {e}")

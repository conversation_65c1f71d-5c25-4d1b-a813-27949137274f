#!/usr/bin/env python3

"""
Simple test to check class inheritance without loading heavy models
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_class_structure():
    """Test the class structure without loading heavy models"""
    try:
        print("Testing class structure...")
        
        # Import the base classes
        from src.vanna.base.base import VannaBase
        from src.vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
        
        print("✓ Base classes imported successfully")
        
        # Create a mock DuckDB chat class that doesn't load the model
        class MockDuckDBNSQL_Chat(VannaBase):
            def __init__(self, config=None):
                VannaBase.__init__(self, config=config)
                self.temperature = 0.1
                self.max_new_tokens = 1024
                
                if config is not None:
                    if "temperature" in config:
                        self.temperature = config["temperature"]
                    if "max_new_tokens" in config:
                        self.max_new_tokens = config["max_new_tokens"]
                
                # Don't load the actual model for testing
                self.model = None
            
            def system_message(self, message: str) -> any:
                return {"role": "system", "content": message}

            def user_message(self, message: str) -> any:
                return {"role": "user", "content": message}

            def assistant_message(self, message: str) -> any:
                return {"role": "assistant", "content": message}

            def submit_prompt(self, prompt, **kwargs) -> str:
                return "Mock response"
        
        print("✓ Mock DuckDB chat class created")
        
        # Create the combined class
        class TestVanna(MockDuckDBNSQL_Chat, ChromaDB_VectorStore):
            def __init__(self, config=None):
                if config is None:
                    config = {}
                
                # Configure for testing
                config["temperature"] = 0.1
                config["max_new_tokens"] = 1024
                config["path"] = "./test_chromadb_store"
                config["client"] = "in-memory"
                
                # Initialize both parent classes
                MockDuckDBNSQL_Chat.__init__(self, config=config)
                ChromaDB_VectorStore.__init__(self, config=config)
        
        print("✓ TestVanna class defined")
        
        # Test instantiation
        print("Testing instantiation...")
        
        # Create a simple embedding function for testing
        class MockEmbeddingFunction:
            def __call__(self, texts):
                # Return mock embeddings
                return [[0.1] * 384 for _ in texts]
        
        test_config = {
            "path": "./test_chromadb_store",
            "client": "in-memory",
            "embedding_function": MockEmbeddingFunction()
        }
        
        vn = TestVanna(config=test_config)
        print("✓ TestVanna instantiated successfully")
        
        # Test that all required methods are available
        print("Testing required methods...")
        required_methods = [
            'system_message', 'user_message', 'assistant_message', 'submit_prompt',
            'add_ddl', 'add_documentation', 'add_question_sql', 'generate_embedding',
            'get_related_ddl', 'get_related_documentation', 'get_similar_question_sql',
            'get_training_data', 'remove_training_data'
        ]
        
        missing_methods = []
        for method in required_methods:
            if hasattr(vn, method):
                print(f"   ✓ {method}")
            else:
                print(f"   ❌ {method} MISSING")
                missing_methods.append(method)
        
        if missing_methods:
            print(f"\n❌ Missing methods: {missing_methods}")
            return False
        else:
            print("\n✅ All required methods are available!")
            
        # Test some basic functionality
        print("\nTesting basic functionality...")
        
        # Test message creation
        sys_msg = vn.system_message("Test system message")
        user_msg = vn.user_message("Test user message")
        asst_msg = vn.assistant_message("Test assistant message")
        
        print(f"✓ System message: {sys_msg}")
        print(f"✓ User message: {user_msg}")
        print(f"✓ Assistant message: {asst_msg}")
        
        # Test prompt submission
        response = vn.submit_prompt([sys_msg, user_msg])
        print(f"✓ Prompt response: {response}")
        
        # Test training data operations
        doc_id = vn.add_documentation("Test documentation")
        print(f"✓ Added documentation with ID: {doc_id}")
        
        ddl_id = vn.add_ddl("CREATE TABLE test (id INT);")
        print(f"✓ Added DDL with ID: {ddl_id}")
        
        sql_id = vn.add_question_sql("What is the test table?", "SELECT * FROM test;")
        print(f"✓ Added question-SQL with ID: {sql_id}")
        
        # Test retrieval
        training_data = vn.get_training_data()
        print(f"✓ Retrieved training data: {len(training_data)} rows")
        
        related_docs = vn.get_related_documentation("test")
        print(f"✓ Retrieved related docs: {len(related_docs)} items")
        
        print("\n🎉 All tests passed! The class structure is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_class_structure()
    if success:
        print("\n✅ Class structure test completed successfully!")
        print("The issue is likely with the model loading, not the class structure.")
    else:
        print("\n❌ Class structure test failed.")
    
    sys.exit(0 if success else 1)

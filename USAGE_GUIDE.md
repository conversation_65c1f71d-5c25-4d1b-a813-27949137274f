# Using DuckDB-NSQL-7B with <PERSON><PERSON>

## Overview

This guide explains how to use the motherduckdb/DuckDB-NSQL-7B-v0.1 model as a replacement for the OpenAI/OpenRouter LLM in Vanna. The implementation uses a singleton pattern to ensure the model is loaded only once, optimizing memory usage.

## Installation

1. Install the required dependencies:

```bash
pip install -r requirements_duckdb_nsql.txt
```

2. Make sure you have sufficient memory (16GB+ RAM) or a CUDA-compatible GPU for optimal performance.

## Basic Usage

There are three main ways to use the DuckDB-NSQL-7B model with Vanna:

### 1. Direct Usage

Use the `test_duckdb_nsql.py` script to see a complete example:

```bash
python test_duckdb_nsql.py
```

This script demonstrates how to:
- Initialize the DuckDB-NSQL-7B model with the singleton pattern
- Connect to a database
- Train with sample data
- Generate SQL queries

### 2. Model Switcher Utility

The `ModelSwitcher` utility allows you to easily switch between different LLM backends:

```bash
# Use DuckDB-NSQL-7B model (default)
python example_model_switcher.py --model duckdb_nsql --question "What are the top 5 users by order count?"

# Use OpenAI/OpenRouter
python example_model_switcher.py --model openai --question "What are the top 5 users by order count?"

# Start the Flask web app with DuckDB-NSQL-7B
python example_model_switcher.py --model duckdb_nsql --run-flask
```

### 3. Benchmark Comparison

Compare the performance of DuckDB-NSQL-7B against OpenAI/OpenRouter:

```bash
# Benchmark both models
python benchmark_models.py --models all

# Benchmark only DuckDB-NSQL-7B
python benchmark_models.py --models duckdb_nsql

# Use custom test questions
python benchmark_models.py --questions my_questions.json
```

## Configuration Options

### DuckDB-NSQL-7B Configuration

```python
config = {
    "temperature": 0.1,  # Controls randomness (lower = more deterministic)
    "max_new_tokens": 1024,  # Maximum tokens to generate
    "device": "cuda"  # or "cpu" (optional, auto-detected if not specified)
}
```

### Vector Store Configuration

```python
vector_config = {
    "embedding_function": ef,  # Embedding function to use
    "n_results": 5,  # Number of similar examples to retrieve
    "path": "./chromadb_store"  # Path to store the vector database
}
```

## Performance Considerations

1. **First Run**: The first time you run the code, it will download the model which may take some time.

2. **Memory Usage**: The DuckDB-NSQL-7B model uses 4-bit quantization to reduce memory usage, but still requires significant RAM or GPU memory.

3. **Speed**: SQL generation with DuckDB-NSQL-7B will be slower than OpenAI/OpenRouter, especially on CPU, but doesn't require an internet connection or API keys.

4. **Quality**: The DuckDB-NSQL-7B model is specifically fine-tuned for SQL generation and may perform better for complex SQL queries compared to general-purpose models.

## Troubleshooting

If you encounter issues:

1. **Out of Memory Errors**: Try using CPU instead of GPU by setting `device="cpu"` in the configuration.

2. **Model Download Issues**: Check your internet connection or download the model manually from Hugging Face.

3. **Slow Performance**: If generation is too slow on CPU, consider using a GPU or switching to OpenAI/OpenRouter for faster results.

## Example Code

```python
from src.vanna.duckdb_nsql.duckdb_nsql_chat import DuckDBNSQL_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore

# Create a custom Vanna class
class MyVanna(ChromaDB_VectorStore, DuckDBNSQL_Chat):
    def __init__(self, config=None):
        if config is None:
            config = {}
        
        # Configure parameters
        config["temperature"] = 0.1
        config["max_new_tokens"] = 1024
        
        # Initialize parent classes
        ChromaDB_VectorStore.__init__(self, config=config)
        DuckDBNSQL_Chat.__init__(self, config=config)

# Create an instance
vn = MyVanna()

# Connect to your database
vn.connect_to_postgres(...)

# Generate SQL
sql = vn.generate_sql("What are the top 10 customers by sales?")
print(sql)
```

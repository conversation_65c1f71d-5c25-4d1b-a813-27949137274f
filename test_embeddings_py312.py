import sys
import os
import numpy as np
from chromadb.utils import embedding_functions
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
from pathlib import Path

# Add the project root to the path so we can import Vanna modules
project_root = Path(os.getcwd())
sys.path.append(str(project_root))

from src.vanna.chromadb.chromadb_vector import ChromaDB_VectorStore

# Test with Traditional Chinese text
traditional_chinese_texts = [
    "這是一個測試句子",  # "This is a test sentence"
    "數據庫查詢優化",    # "Database query optimization"
    "自然語言處理技術",   # "Natural language processing technology"
    "今天天氣很好",       # "The weather is nice today"
    "機器學習模型訓練"     # "Machine learning model training"
]

print("\n=== Testing Multilingual Embedding Function with Python 3.12 ===\n")

# Initialize the SentenceTransformer embedding function with a multilingual model
try:
    # Use a multilingual model that's good for Traditional Chinese
    ef = SentenceTransformerEmbeddingFunction(model_name="intfloat/multilingual-e5-base")
    
    # Generate embeddings
    embeddings = ef(traditional_chinese_texts)
    
    # Show the results
    for text, embedding in zip(traditional_chinese_texts, embeddings):
        print(f"Text: {text}")
        print(f"Embedding shape: {np.array(embedding).shape}")
        print(f"First 5 dimensions: {np.array(embedding)[:5]}")
        print("-" * 50)
    
    print("\n=== Testing with Vanna ChromaDB Integration ===\n")
    
    # Create a Vanna ChromaDB instance with the multilingual embedding function
    config = {
        "client": "in-memory",  # Use in-memory for testing
        "embedding_function": ef
    }
    
    vn = ChromaDB_VectorStore(config=config)
    
    # Test adding some Traditional Chinese documentation
    for text in traditional_chinese_texts:
        doc_id = vn.add_documentation(text)
        print(f"Added document with ID: {doc_id}")
    
    # Test querying with Traditional Chinese
    query = "數據庫"  # "database"
    results = vn.get_related_documentation(query)
    
    print(f"\nQuery: {query}")
    print(f"Found {len(results)} related documents:")
    for i, doc in enumerate(results):
        print(f"{i+1}. {doc}")

except Exception as e:
    print(f"Error testing embeddings: {e}")

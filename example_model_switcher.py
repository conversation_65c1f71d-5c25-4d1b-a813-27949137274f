import os
import sys

# Import the model switcher utility
from src.vanna.utils.model_switcher import ModelSwitcher, MODEL_TYPE_OPENAI, MODEL_TYPE_DUCKDB_NSQL
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction

import vanna
from vanna.flask import VannaFlaskApp

# Monkey patch to prevent kaleido import
sys.modules['kaleido'] = type('', (), {})()
sys.modules['kaleido.scopes'] = type('', (), {})()
sys.modules['kaleido.scopes.plotly'] = type('', (), {'PlotlyScope': type('', (), {'render': lambda *args, **kwargs: b''})})

# Set environment variable for plotly
os.environ["PLOTLY_RENDERER"] = "svg"

# Create a singleton class for the embedding function to avoid reloading the model each time
class EmbeddingFunctionSingleton:
    _instance = None
    _model_path = './embedding_model_cache'
    _model_name = "intfloat/multilingual-e5-base"
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            print("Loading embedding model for the first time...")
            cls._instance = SentenceTransformerEmbeddingFunction(model_name=cls._model_name, cache_folder=cls._model_path)
            print("Embedding model loaded successfully.")
        return cls._instance

# Get the embedding function instance
ef = EmbeddingFunctionSingleton.get_instance()

# Configure vector store settings
vector_config = {
    "embedding_function": ef,
    "n_results": 5,  # Number of similar examples to retrieve
    "path": "./chromadb_store"  # Path to store the vector database
}

def create_vanna_instance(model_type):
    """Create a Vanna instance with the specified model type."""
    if model_type == MODEL_TYPE_OPENAI:
        # Configure OpenAI/OpenRouter
        model_config = ModelSwitcher.configure_openai(
            api_key='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',
            base_url="https://openrouter.ai/api/v1",
            model="gpt-3.5-turbo"  # You can specify a different model here
        )
        print("Using OpenAI/OpenRouter as the LLM backend")
    
    elif model_type == MODEL_TYPE_DUCKDB_NSQL:
        # Configure DuckDB-NSQL
        model_config = ModelSwitcher.configure_duckdb_nsql(
            temperature=0.1,
            max_new_tokens=1024,
            # Uncomment the line below to force CPU usage
            # device="cpu"
        )
        print("Using DuckDB-NSQL-7B as the LLM backend")
    
    else:
        raise ValueError(f"Invalid model type: {model_type}")
    
    # Create and return the Vanna instance
    return ModelSwitcher.create_vanna_instance(
        model_type=model_type,
        vector_store_class=ChromaDB_VectorStore,
        model_config=model_config,
        vector_config=vector_config
    )

def main():
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Vanna Model Switcher Example")
    parser.add_argument("--model", choices=["openai", "duckdb_nsql"], default="duckdb_nsql",
                        help="Model type to use (openai or duckdb_nsql)")
    parser.add_argument("--run-flask", action="store_true", help="Run the Flask app")
    parser.add_argument("--question", type=str, help="Question to generate SQL for")
    args = parser.parse_args()
    
    # Create Vanna instance with the specified model
    vn = create_vanna_instance(args.model)
    
    # Connect to a database (using PostgreSQL as an example)
    # Replace with your own database connection details
    try:
        vn.connect_to_postgres(
            host='************',
            dbname='clinical_db',
            user='nhri001',
            password='cbit2012',
            port=5432
        )
        print("Connected to PostgreSQL database")
    except Exception as e:
        print(f"Error connecting to database: {str(e)}")
        print("Continuing without database connection...")
    
    # If a question was provided, generate SQL for it
    if args.question:
        try:
            print(f"\nGenerating SQL for question: {args.question}")
            sql = vn.generate_sql(args.question)
            print(f"Generated SQL:\n{sql}")
            
            # Try to execute the SQL if database is connected
            if vn.run_sql_is_set:
                try:
                    result_df = vn.run_sql(sql)
                    print("\nQuery Result:")
                    print(result_df.head())
                except Exception as e:
                    print(f"Error executing SQL: {str(e)}")
        except Exception as e:
            print(f"Error generating SQL: {str(e)}")
    
    # Run the Flask app if requested
    if args.run_flask:
        print("\nStarting Flask app...")
        # Disable figure as image to avoid kaleido dependency
        vanna.fig_as_img = False
        VannaFlaskApp(vn).run()

if __name__ == "__main__":
    main()

import sys
# Import our custom DuckDB-NSQL-7B model implementation
from src.vanna.duckdb_nsql.duckdb_nsql_chat import DuckDBNSQL_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
import os.path
from src.trainfile import Train
import vanna
from vanna.flask import VannaFlaskApp

# Monkey patch to prevent kaleido import
sys.modules['kaleido'] = type('', (), {})()
sys.modules['kaleido.scopes'] = type('', (), {})() 
sys.modules['kaleido.scopes.plotly'] = type('', (), {'PlotlyScope': type('', (), {'render': lambda *args, **kwargs: b''})})

# Set environment variable for plotly
os.environ["PLOTLY_RENDERER"] = "svg"

# Create a custom Vanna class with DuckDB-NSQL-7B model and vector store
class MyVanna(ChromaDB_VectorStore, DuckDBNSQL_Chat):
    def __init__(self, config=None):
        if config is None:
            config = {}
        
        # Configure the DuckDB-NSQL-7B model
        config["temperature"] = 0.1  # Lower temperature for more deterministic SQL generation
        config["max_new_tokens"] = 1024  # Maximum tokens to generate
        
        # Configure ChromaDB vector store
        config["path"] = "./chromadb_store"  # Path to store the vector database
        
        # Initialize both parent classes
        ChromaDB_VectorStore.__init__(self, config=config)
        DuckDBNSQL_Chat.__init__(self, config=config)
    
    # Remove these methods - they're already defined in DuckDBNSQL_Chat
    # def system_message(self, message: str) -> any:
    #     return DuckDBNSQL_Chat.system_message(self, message)
    
    # def user_message(self, message: str) -> any:
    #     return DuckDBNSQL_Chat.user_message(self, message)
    
    # def assistant_message(self, message: str) -> any:
    #     return DuckDBNSQL_Chat.assistant_message(self, message)
    
    # def submit_prompt(self, prompt, **kwargs) -> str:
    #     return DuckDBNSQL_Chat.submit_prompt(self, prompt, **kwargs)

# Create a singleton class for the embedding function to avoid reloading the model each time
class EmbeddingFunctionSingleton:
    _instance = None
    _model_path = './embedding_model_cache'
    _model_name = "intfloat/multilingual-e5-base"
    
    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            print("Loading embedding model for the first time...")
            cls._instance = SentenceTransformerEmbeddingFunction(model_name=cls._model_name, cache_folder=cls._model_path)
            print("Embedding model loaded successfully.")
        return cls._instance

# Get the embedding function instance
ef = EmbeddingFunctionSingleton.get_instance()

# Create an instance of our custom Vanna class
vn = MyVanna(config={
    "embedding_function": ef,
    "n_results": 5  # Number of similar examples to retrieve
})

# Connect to a database (using PostgreSQL as an example)
vn.connect_to_postgres(
    host='************',
    dbname='clinical_db',
    user='nhri001',
    password='cbit2012',
    port=5432
)

train = Train(vn)

try:
    # Get schema for a table and save as JSON
    #train.get_table_schema_to_json('casedauqc')    
    # Train with DLL definitions from JSON files
    train.train_from_dll_define_json()
    print("Trained with DLL definitions from JSON files")
except Exception as e:
    print(f"Error during DLL training: {str(e)}")


# Disable figure as image to avoid kaleido dependency
vanna.fig_as_img = False

# Start the Flask app
print("\nStarting Flask app...")
VannaFlaskApp(vn).run()

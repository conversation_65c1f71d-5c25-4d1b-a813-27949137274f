import os
import sys
import json

from openai import OpenAI
from vanna.openai.openai_chat import OpenAI_Chat
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams, PointStruct
from sentence_transformers import SentenceTransformer
import os.path

import vanna
from vanna.flask import <PERSON>na<PERSON><PERSON>kA<PERSON>
from src.trainfile import Train
import voyageai
from typing import List, Dict, Any
from vanna.qdrant.qdrant import Qdrant_VectorStore


# Monkey patch to prevent kaleido import
sys.modules['kaleido'] = type('', (), {})()
sys.modules['kaleido.scopes'] = type('', (), {})()
sys.modules['kaleido.scopes.plotly'] = type('', (), {'PlotlyScope': type('', (), {'render': lambda *args, **kwargs: b''})})

# Set environment variable for plotly
os.environ["PLOTLY_RENDERER"] = "svg"

# Now import vanna components

openai_client = OpenAI(api_key='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', base_url="https://openrouter.ai/api/v1")

# Create a custom Vanna class with your preferred LLM and vector store
class QdrantVectorStore:
    def __init__(self, config=None):
        self.config = config or {}
        self.client = QdrantClient(
            url=self.config.get("qdrant_url", "http://localhost:6333"),
            api_key=self.config.get("qdrant_api_key", None),
        )
        self.collection_name = self.config.get("collection_name", "vanna_embeddings")
        self.embedding_model = None
        self.dimension = None
        self._init_embedding_model()
        self._ensure_collection()

    def _init_embedding_model(self):
        model_name = self.config.get("embedding_model", "intfloat/multilingual-e5-base")
        cache_folder = self.config.get("embedding_model_cache", "./embedding_model_cache")
        print(f"Loading embedding model: {model_name}")
        self.embedding_model = SentenceTransformer(model_name, cache_folder=cache_folder)
        # Get the dimension by creating a dummy embedding
        self.dimension = self.embedding_model.get_sentence_embedding_dimension()
        print(f"Embedding model loaded successfully. Dimension: {self.dimension}")

    def _ensure_collection(self):
        collections = self.client.get_collections().collections
        collection_names = [collection.name for collection in collections]

        if self.collection_name not in collection_names:
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(size=self.dimension, distance=Distance.COSINE),
            )
            print(f"Created new collection: {self.collection_name}")

    def add_documents(self, documents: List[Dict[str, Any]]) -> None:
        if not documents:
            return

        # Embed the documents
        texts = [doc.get("document") for doc in documents]
        embeddings = self.embedding_model.encode(texts, show_progress_bar=True).tolist()

        # Prepare points for Qdrant
        points = []
        for idx, (doc, embedding) in enumerate(zip(documents, embeddings)):
            point = PointStruct(
                id=idx + 1,  # Simple ID generation, you might want something more robust
                vector=embedding,
                payload={
                    "document": doc.get("document"),
                    "metadata": doc.get("metadata", {}),
                    "id": doc.get("id"),
                }
            )
            points.append(point)

        # Upload to Qdrant
        self.client.upsert(
            collection_name=self.collection_name,
            points=points,
            wait=True
        )

    def similarity_search(self, query: str, k: int = 5, **kwargs) -> List[Dict[str, Any]]:
        # Embed the query
        query_embedding = self.embedding_model.encode(query).tolist()

        # Search in Qdrant
        search_result = self.client.search(
            collection_name=self.collection_name,
            query_vector=query_embedding,
            limit=k,
            with_payload=True,
            with_vectors=False
        )

        # Format the results
        results = []
        for hit in search_result:
            results.append({
                "document": hit.payload.get("document"),
                "metadata": hit.payload.get("metadata", {}),
                "id": hit.payload.get("id"),
                "score": hit.score
            })

        return results

class MyVanna(Qdrant_VectorStore, OpenAI_Chat):
    def __init__(self, config=None):
        Qdrant_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, client=openai_client, config=config)
        # Store the Qdrant client separately to avoid conflicts with OpenAI client
        self.qdrant_client = self._client

    def add_documentation(self, documentation: str, **kwargs) -> str:
        """
        Enhanced add_documentation that supports metadata including filename and document info
        """
        from vanna.utils import deterministic_uuid
        from qdrant_client import models

        id = deterministic_uuid(documentation)

        # Prepare payload with documentation and metadata
        payload = {
            "documentation": documentation,
        }

        # Add metadata if provided
        if kwargs:
            # Extract common metadata fields
            filename = kwargs.get('filename', kwargs.get('source_file', ''))
            document_type = kwargs.get('document_type', 'unknown')
            chunk_index = kwargs.get('chunk_index', 0)
            total_chunks = kwargs.get('total_chunks', 1)
            source_path = kwargs.get('source_path', '')

            payload.update({
                'filename': filename,
                'document_type': document_type,
                'chunk_index': chunk_index,
                'total_chunks': total_chunks,
                'source_path': source_path,
                'metadata': kwargs  # Store all metadata
            })

        self.qdrant_client.upsert(
            self.documentation_collection_name,
            points=[
                models.PointStruct(
                    id=id,
                    vector=self.generate_embedding(documentation),
                    payload=payload,
                )
            ],
        )

        return self._format_point_id(id, self.documentation_collection_name)

    def search_documents(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant documents using semantic search"""
        try:
            # Use the Qdrant client directly from the instance
            if not hasattr(self, 'qdrant_client'):
                raise ValueError("Qdrant client not initialized")

            # Generate embedding for the query using the same method as Qdrant_VectorStore
            # This ensures dimension compatibility
            query_embedding = self.generate_embedding(query)

            # Get the collection name from the instance or use default
            # Use the documentation collection for document search
            collection_name = getattr(self, 'documentation_collection_name', 'documentation')

            # Search in Qdrant using the correct client
            search_result = self.qdrant_client.search(
                collection_name=collection_name,
                query_vector=query_embedding,
                limit=k,
                with_payload=True
            )

            # Format results
            results = []
            for hit in search_result:
                if hasattr(hit, 'payload') and hasattr(hit, 'score'):
                    # Try to get content from different possible keys
                    content = (hit.payload.get("documentation", "") or
                              hit.payload.get("document", "") or
                              hit.payload.get("content", ""))

                    # Extract metadata including filename
                    result = {
                        "content": content,
                        "score": float(hit.score),
                        "filename": hit.payload.get("filename", ""),
                        "document_type": hit.payload.get("document_type", "unknown"),
                        "chunk_index": hit.payload.get("chunk_index", 0),
                        "total_chunks": hit.payload.get("total_chunks", 1),
                        "source_path": hit.payload.get("source_path", ""),
                        "metadata": hit.payload.get("metadata", {}),
                    }
                    results.append(result)

            return results
        except Exception as e:
            print(f"Error searching documents: {e}")
            import traceback
            traceback.print_exc()
            return []

    def search_receipt_info(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        Enhanced search for receipt information that handles PDF text extraction issues
        like extra spaces in invoice numbers and other identifiers
        """
        try:
            # First try the original query
            results = self.search_documents(query, k)

            # If no results and the query looks like an invoice number or ID, try variations
            if not results and len(query) > 5:
                # Create variations of the query to handle PDF spacing issues
                variations = [query]

                # Add spaces between characters for alphanumeric strings
                if query.replace(' ', '').isalnum():
                    spaced_query = ' '.join(query.replace(' ', ''))
                    variations.append(spaced_query)

                # Try partial matches for long identifiers
                if len(query) > 8:
                    # Split into chunks and try each
                    mid_point = len(query) // 2
                    variations.extend([
                        query[:mid_point],
                        query[mid_point:],
                        query[:6],  # First 6 characters
                        query[-6:]  # Last 6 characters
                    ])

                # Try each variation
                for variation in variations[1:]:  # Skip the original query
                    if variation and variation != query:
                        variation_results = self.search_documents(variation, k)
                        if variation_results:
                            # Add a note about which variation found results
                            for result in variation_results:
                                result['search_variation'] = variation
                            results.extend(variation_results)
                            break

            # Also try semantic searches for receipt-related terms
            if not results:
                semantic_queries = [
                    "receipt invoice payment",
                    "OpenAI API usage billing",
                    "September 2024 invoice",
                    "Richard Chang payment",
                    "Mastercard payment receipt"
                ]

                for semantic_query in semantic_queries:
                    semantic_results = self.search_documents(semantic_query, k)
                    if semantic_results:
                        for result in semantic_results:
                            result['search_type'] = 'semantic'
                        results.extend(semantic_results)
                        break

            # Remove duplicates based on content
            seen_content = set()
            unique_results = []
            for result in results:
                content_hash = hash(result.get('content', '')[:100])
                if content_hash not in seen_content:
                    seen_content.add(content_hash)
                    unique_results.append(result)

            return unique_results[:k]

        except Exception as e:
            print(f"Error in enhanced receipt search: {e}")
            return self.search_documents(query, k)  # Fallback to regular search

    def get_document_summary(self) -> Dict[str, Any]:
        """
        Get a comprehensive summary of all trained documents including counts and types
        """
        try:
            summary = {
                "total_documents": 0,
                "document_types": {},
                "collections": {},
                "sample_documents": [],
                "document_names": [],
                "unique_files": set()
            }

            # Get all documentation points
            doc_points = self._get_all_points(self.documentation_collection_name)

            if doc_points:
                summary["total_documents"] = len(doc_points)
                summary["collections"]["documentation"] = len(doc_points)

                # Analyze document types and content
                receipt_docs = []
                ddl_docs = []
                other_docs = []

                for point in doc_points:
                    content = point.payload.get("documentation", "")
                    content_lower = content.lower()
                    filename = point.payload.get("filename", "")
                    document_type = point.payload.get("document_type", "unknown")
                    chunk_index = point.payload.get("chunk_index", 0)
                    total_chunks = point.payload.get("total_chunks", 1)

                    # Track unique filenames
                    if filename:
                        summary["unique_files"].add(filename)

                    # Create document info with metadata
                    doc_info = {
                        "type": document_type,
                        "filename": filename,
                        "chunk_index": chunk_index,
                        "total_chunks": total_chunks,
                        "content_preview": content[:200] + "..." if len(content) > 200 else content,
                        "id": str(point.id)
                    }

                    # Categorize documents by content if type is unknown
                    if document_type == "unknown":
                        if any(keyword in content_lower for keyword in ["receipt", "invoice", "payment", "billing"]):
                            doc_info["type"] = "receipt"
                            receipt_docs.append(doc_info)
                        elif any(keyword in content_lower for keyword in ["create table", "ddl", "database", "schema"]):
                            doc_info["type"] = "ddl_documentation"
                            ddl_docs.append(doc_info)
                        else:
                            doc_info["type"] = "other"
                            other_docs.append(doc_info)
                    else:
                        # Use the provided document type
                        if document_type in ["receipt", "invoice", "payment"]:
                            receipt_docs.append(doc_info)
                        elif document_type in ["ddl", "database", "schema"]:
                            ddl_docs.append(doc_info)
                        else:
                            other_docs.append(doc_info)

                summary["document_types"] = {
                    "receipts": len(receipt_docs),
                    "ddl_documentation": len(ddl_docs),
                    "other": len(other_docs)
                }

                # Convert unique files set to sorted list
                summary["document_names"] = sorted(list(summary["unique_files"]))
                summary["unique_files"] = len(summary["unique_files"])

                # Add sample documents
                summary["sample_documents"] = (receipt_docs[:2] + ddl_docs[:2] + other_docs[:2])[:5]

            # Get DDL collection info
            ddl_points = self._get_all_points(self.ddl_collection_name)
            if ddl_points:
                summary["collections"]["ddl"] = len(ddl_points)
                summary["total_documents"] += len(ddl_points)

            # Get SQL collection info
            sql_points = self._get_all_points(self.sql_collection_name)
            if sql_points:
                summary["collections"]["sql"] = len(sql_points)
                summary["total_documents"] += len(sql_points)

            return summary

        except Exception as e:
            print(f"Error getting document summary: {e}")
            return {"error": str(e), "total_documents": 0}

    def search_all_documents(self, query: str = "", k: int = 20) -> List[Dict[str, Any]]:
        """
        Search across all document types with enhanced filtering for summary queries
        """
        try:
            all_results = []

            # If query is about counting or listing documents, get all documents
            count_keywords = ["how many", "count", "list all", "show all", "summary", "total"]
            is_count_query = any(keyword in query.lower() for keyword in count_keywords)

            if is_count_query or not query.strip():
                # Get all documents from documentation collection
                doc_points = self._get_all_points(self.documentation_collection_name)

                for point in doc_points:
                    content = point.payload.get("documentation", "")
                    filename = point.payload.get("filename", "")
                    document_type = point.payload.get("document_type", "unknown")
                    chunk_index = point.payload.get("chunk_index", 0)
                    total_chunks = point.payload.get("total_chunks", 1)

                    all_results.append({
                        "content": content,
                        "score": 1.0,  # Max score for direct retrieval
                        "type": "documentation",
                        "filename": filename,
                        "document_type": document_type,
                        "chunk_index": chunk_index,
                        "total_chunks": total_chunks,
                        "id": str(point.id),
                        "collection": self.documentation_collection_name
                    })
            else:
                # Use semantic search for specific queries
                semantic_results = self.search_documents(query, k)
                all_results.extend(semantic_results)

            # Sort by relevance (receipts first for receipt-related queries)
            if any(keyword in query.lower() for keyword in ["receipt", "invoice", "payment", "billing"]):
                def sort_key(doc):
                    content_lower = doc["content"].lower()
                    if any(keyword in content_lower for keyword in ["receipt", "invoice", "payment"]):
                        return (0, -doc["score"])  # Receipts first, then by score
                    return (1, -doc["score"])

                all_results.sort(key=sort_key)

            return all_results[:k]

        except Exception as e:
            print(f"Error in search_all_documents: {e}")
            return []

    def get_enhanced_prompt_context(self, question: str) -> str:
        """
        Generate enhanced context for prompts that includes document summaries
        """
        try:
            # Check if this is a summary/count type question
            summary_keywords = ["how many", "count", "list", "show all", "summary", "what documents"]
            is_summary_question = any(keyword in question.lower() for keyword in summary_keywords)

            if is_summary_question:
                # Get document summary
                summary = self.get_document_summary()

                context = f"""
DOCUMENT SUMMARY:
- Total documents trained: {summary.get('total_documents', 0)}
- Document types: {summary.get('document_types', {})}
- Collections: {summary.get('collections', {})}

SAMPLE DOCUMENTS:
"""
                for i, doc in enumerate(summary.get('sample_documents', []), 1):
                    context += f"{i}. Type: {doc['type']}\n   Preview: {doc['content_preview']}\n\n"

                return context
            else:
                # For specific questions, use regular documentation retrieval
                docs = self.get_related_documentation(question)
                if docs:
                    return "\n\n".join(docs[:3])  # Top 3 most relevant
                return ""

        except Exception as e:
            print(f"Error generating enhanced context: {e}")
            return ""

    def get_related_documentation(self, question: str, **kwargs) -> list:
        """
        Enhanced version of get_related_documentation that handles summary queries better
        """
        try:
            # Check if this is a summary/count type question
            summary_keywords = ["how many", "count", "list", "show all", "summary", "what documents", "total documents"]
            is_summary_question = any(keyword in question.lower() for keyword in summary_keywords)

            if is_summary_question:
                # For summary questions, return all documents with type information
                all_docs = self.search_all_documents(question, k=50)

                # Format as documentation strings with type info
                formatted_docs = []
                for doc in all_docs:
                    doc_type = "Unknown"
                    content = doc["content"]
                    content_lower = content.lower()

                    if any(keyword in content_lower for keyword in ["receipt", "invoice", "payment", "billing"]):
                        doc_type = "Receipt/Invoice"
                    elif any(keyword in content_lower for keyword in ["create table", "ddl", "database"]):
                        doc_type = "Database Documentation"
                    elif any(keyword in content_lower for keyword in ["table", "column", "field"]):
                        doc_type = "Table Documentation"

                    formatted_doc = f"[{doc_type}] {content[:500]}..." if len(content) > 500 else f"[{doc_type}] {content}"
                    formatted_docs.append(formatted_doc)

                return formatted_docs
            else:
                # For specific questions, use the parent class method
                return super().get_related_documentation(question, **kwargs)

        except Exception as e:
            print(f"Error in enhanced get_related_documentation: {e}")
            # Fallback to parent method
            return super().get_related_documentation(question, **kwargs)

    def train_from_pdf_with_metadata(self, pdf_path, chunk_size=1000, overlap=200):
        """
        Enhanced PDF training that preserves document metadata including filename
        """
        import os
        import PyPDF2
        from pathlib import Path

        try:
            # Check if file exists
            if not os.path.isfile(pdf_path):
                print(f"Error: File {pdf_path} does not exist")
                return 0

            # Open PDF file
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)

                # Extract text from all pages
                text = ""
                for page in reader.pages:
                    text += page.extract_text() + "\n"

                # Split text into chunks with overlap
                chunks = []
                for i in range(0, len(text), chunk_size - overlap):
                    chunk = text[i:i + chunk_size]
                    chunks.append(chunk)

                # Get file metadata
                file_path = Path(pdf_path)
                filename = file_path.name

                # Train with each chunk including metadata
                for i, chunk in enumerate(chunks):
                    metadata = {
                        'filename': filename,
                        'source_path': str(pdf_path),
                        'document_type': 'pdf',
                        'chunk_index': i,
                        'total_chunks': len(chunks)
                    }

                    # Use the enhanced add_documentation method
                    self.add_documentation(chunk, **metadata)
                    print(f"Trained chunk {i+1}/{len(chunks)} from {filename}")

                print(f"Successfully trained {len(chunks)} chunks from {pdf_path}")
                return len(chunks)

        except Exception as e:
            print(f"Error processing PDF {pdf_path}: {str(e)}")
            return 0

    def train_from_pdf_directory_with_metadata(self, directory_path, chunk_size=1000, overlap=200):
        """
        Enhanced PDF directory training that preserves document metadata
        """
        import os
        from pathlib import Path

        try:
            # Check if directory exists
            if not os.path.isdir(directory_path):
                print(f"Error: Directory {directory_path} does not exist")
                return (0, 0)

            # Get all PDF files in the directory
            pdf_files = list(Path(directory_path).glob('**/*.pdf'))

            if not pdf_files:
                print(f"No PDF files found in {directory_path}")
                return (0, 0)

            print(f"Found {len(pdf_files)} PDF files in {directory_path}")

            # Process each PDF file
            total_chunks = 0
            processed_files = 0

            for pdf_file in pdf_files:
                chunks = self.train_from_pdf_with_metadata(str(pdf_file), chunk_size, overlap)
                if chunks > 0:
                    total_chunks += chunks
                    processed_files += 1

            print(f"Successfully processed {processed_files}/{len(pdf_files)} PDF files with {total_chunks} total chunks")
            return (processed_files, total_chunks)

        except Exception as e:
            print(f"Error processing PDF directory: {str(e)}")
            return (0, 0)

    def get_document_names(self) -> List[str]:
        """
        Get a list of all unique document names/filenames that have been trained
        """
        try:
            doc_points = self._get_all_points(self.documentation_collection_name)
            unique_files = set()

            for point in doc_points:
                filename = point.payload.get("filename", "")
                if filename:
                    unique_files.add(filename)

            return sorted(list(unique_files))

        except Exception as e:
            print(f"Error getting document names: {e}")
            return []

    def get_sql_prompt(
        self,
        question: str,
        question_sql_list: List[Dict[str, str]],
        ddl_list: List[str],
        doc_list: List[str],
        **kwargs
    ) -> List[Dict[str, str]]:
        # Search for relevant documents
        relevant_docs = self.search_documents(question)

        # Create a system prompt focused on document search
        system_prompt = """You are a helpful assistant that answers questions based on the provided document context.
        Your task is to analyze the question and provide a clear, concise answer using only the information from the documents.
        If the answer cannot be found in the documents, say 'I don't have enough information to answer this question.'
        """

        # Add document context to the prompt
        if relevant_docs:
            context = "Document context for your reference:\n\n"
            for i, doc in enumerate(relevant_docs, 1):
                context += f"--- Document {i} (relevance: {doc['score']:.2f}) ---\n{doc['content']}\n\n"

            # Create the messages list
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "system", "content": context},
                {"role": "user", "content": f"Question: {question}"}
            ]

            return messages
        else:
            return [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Question: {question}"}
            ]

    def generate_sql(self, question: str, **kwargs) -> str:
        # Override to prevent SQL generation and focus on document search
        relevant_docs = self.search_documents(question, k=3)

        if not relevant_docs:
            return "I couldn't find any relevant information to answer your question."

        # Format the document context
        context = "Relevant information from documents:\n\n"
        for i, doc in enumerate(relevant_docs, 1):
            context += f"--- Document {i} (relevance: {doc['score']:.2f}) ---\n{doc['content']}\n\n"

        # Create a prompt for the LLM to answer based on the documents
        messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant that answers questions based on the provided document context. "
                          "If the answer cannot be found in the documents, say 'I don't have enough information to answer this question.'"
            },
            {
                "role": "user",
                "content": f"Based on the following documents, answer this question: {question}\n\n{context}"
            }
        ]

        # Get the response from the LLM
        response = self.submit_prompt(messages, **kwargs)
        return response


class VoyageAIEmbeddingFunction:
    def __init__(self, api_key=None, model_name="voyage-2", dimensions=1024):
        """
        Initialize the VoyageAI embedding function.

        Args:
            api_key: Your VoyageAI API key. If None, it will try to use the VOYAGE_API_KEY environment variable.
            model_name: The model to use for embeddings. Default is "voyage-2".
            dimensions: The dimension of the embeddings. Default is 1024 for voyage-2.
        """
        self.api_key = api_key or os.environ.get("VOYAGE_API_KEY")
        if not self.api_key:
            raise ValueError("VoyageAI API key is required. Set it as VOYAGE_API_KEY environment variable or pass it to the constructor.")

        self.client = voyageai.Client(api_key=self.api_key)
        self.model_name = model_name
        self.dimensions = dimensions

    def __call__(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for the given texts.

        Args:
            texts: A list of strings to generate embeddings for.

        Returns:
            A list of embeddings, one for each text.
        """
        if not texts:
            return []

        try:
            # For a single text, we need to handle it differently
            if len(texts) == 1:
                # Generate embedding for a single text
                embedding = self.client.embed(
                    text=texts[0],  # Pass as a single text, not a list
                    model=self.model_name,
                    input_type="document"
                )

                # Convert to list and return as a list of lists (with one element)
                if hasattr(embedding, 'tolist'):
                    return [embedding.tolist()]
                else:
                    return [list(embedding)]
            else:
                # For multiple texts, use batch processing
                # Process each text individually to avoid the subscriptable error
                result = []
                for text in texts:
                    embedding = self.client.embed(
                        text=text,  # Process one at a time
                        model=self.model_name,
                        input_type="document"
                    )

                    # Convert to list
                    if hasattr(embedding, 'tolist'):
                        result.append(embedding.tolist())
                    else:
                        result.append(list(embedding))

                return result

        except Exception as e:
            print(f"Error generating embeddings with VoyageAI: {str(e)}")
            # Return empty embeddings of the right dimension as a fallback
            return [[0.0] * self.dimensions for _ in texts]

# Initialize Qdrant vector store with configuration
qdrant_config = {
    "qdrant_url": "http://localhost:6333",  # Update with your Qdrant server URL
    "qdrant_api_key": None,  # Add your API key if using Qdrant Cloud
    "collection_name": "vanna_embeddings",
    "embedding_model": "intfloat/multilingual-e5-base",
    "embedding_model_cache": "./embedding_model_cache"
}

# Initialize Vanna with Qdrant
vn = MyVanna(config={
    **qdrant_config,
    "model": "gpt-4-0125-preview"
})
vn = MyVanna(config={'api_key': '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'
                        , 'model': 'mistralai/devstral-small'
                        , 'url': 'http://localhost:6333'
                        })


# Connect to your database
#vn.connect_to_postgres(
#    host='ep-shy-base-a1r7sb9g-pooler.ap-southeast-1.aws.neon.tech',
#    database='verceldb',
#    user='default',
#    password='sw5paT9xJLGY',
#    port=5432,
#    sslmode='require'
#)

vn.connect_to_postgres(
    host='************',
    dbname='clinical_db',
    user='nhri001',
    password='cbit2012',
    port=5432
)

# Initialize the Tran class with our Vanna instance

train = Train(vn)

try:
    # Get schema for a table and save as JSON
    #train.get_table_schema_to_json('casedauqc')
    # Train with DLL definitions from JSON files
    train.train_from_dll_define_json()
    print("Trained with DLL definitions from JSON files")

    # Use enhanced PDF training with metadata preservation
    vn.train_from_pdf_directory_with_metadata('training_data/pdf')
except Exception as e:
    print(f"Error during training: {str(e)}")

# Disable figure as image to avoid kaleido dependency
vanna.fig_as_img = False



# Continue with your code
VannaFlaskApp(vn).run()

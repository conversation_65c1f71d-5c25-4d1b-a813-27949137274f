FROM python:3.10-slim

WORKDIR /app

# Install dependencies without kaleido
RUN pip install --no-cache-dir requests tabulate plotly pandas sqlparse flask flask-sock flasgger sqlalchemy
RUN pip install --no-cache-dir openai chromadb<1.0.0
RUN pip install --no-cache-dir "vanna[openai,chromadb]" --no-deps

# Copy your code
COPY test.py .

# Expose port for Flask app
EXPOSE 5000

# Run the application
CMD ["python", "test.py"]

from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from vanna.openai.openai_chat import OpenAI_Chat

class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config={
            "path": "/path/to/your/chroma_db"  # Specify your desired path here
        })
        OpenAI_Chat.__init__(self, config=config)

# Or when initializing directly:
vn = ChromaDB_VectorStore(config={
    "path": "/path/to/your/chroma_db"
})
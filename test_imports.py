#!/usr/bin/env python3

"""
Simple test to check if imports work correctly
"""

import sys
import os

print("Testing imports...")

try:
    print("1. Testing DuckDBNSQL_Chat import...")
    from src.vanna.duckdb_nsql.duckdb_nsql_chat import DuckDBNSQL_Chat
    print("   ✓ DuckDBNSQL_Chat imported successfully")
except Exception as e:
    print(f"   ❌ Error importing DuckDBNSQL_Chat: {e}")
    sys.exit(1)

try:
    print("2. Testing ChromaDB_VectorStore import...")
    from src.vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
    print("   ✓ ChromaDB_VectorStore imported successfully")
except Exception as e:
    print(f"   ❌ Error importing ChromaDB_VectorStore: {e}")
    sys.exit(1)

try:
    print("3. Testing SentenceTransformerEmbeddingFunction import...")
    from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
    print("   ✓ SentenceTransformerEmbeddingFunction imported successfully")
except Exception as e:
    print(f"   ❌ Error importing SentenceTransformerEmbeddingFunction: {e}")
    sys.exit(1)

try:
    print("4. Testing MyVanna class creation...")
    
    class MyVanna(DuckDBNSQL_Chat, ChromaDB_VectorStore):
        def __init__(self, config=None):
            if config is None:
                config = {}
            
            # Configure the DuckDB-NSQL-7B model
            config["temperature"] = 0.1
            config["max_new_tokens"] = 1024
            
            # Configure ChromaDB vector store
            config["path"] = "./chromadb_store"
            
            # Initialize both parent classes
            DuckDBNSQL_Chat.__init__(self, config=config)
            ChromaDB_VectorStore.__init__(self, config=config)
    
    print("   ✓ MyVanna class defined successfully")
    
    # Test instantiation without actually loading the model
    print("5. Testing MyVanna instantiation (without model loading)...")
    
    # Create a minimal config that won't trigger model loading
    test_config = {
        "path": "./test_chromadb_store",
        "temperature": 0.1,
        "max_new_tokens": 1024,
        "client": "in-memory"  # Use in-memory ChromaDB to avoid file system issues
    }
    
    # Create embedding function
    ef = SentenceTransformerEmbeddingFunction(model_name="intfloat/multilingual-e5-base")
    test_config["embedding_function"] = ef
    
    print("   Creating MyVanna instance...")
    vn = MyVanna(config=test_config)
    print("   ✓ MyVanna instantiated successfully")
    
    # Test that all required methods are available
    print("6. Testing required methods...")
    required_methods = [
        'system_message', 'user_message', 'assistant_message', 'submit_prompt',
        'add_ddl', 'add_documentation', 'add_question_sql', 'generate_embedding',
        'get_related_ddl', 'get_related_documentation', 'get_similar_question_sql',
        'get_training_data', 'remove_training_data'
    ]
    
    for method in required_methods:
        if hasattr(vn, method):
            print(f"   ✓ {method} method available")
        else:
            print(f"   ❌ {method} method missing")
    
    print("\n✅ All tests passed! The class should work correctly.")
    
except Exception as e:
    print(f"   ❌ Error creating MyVanna class: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\nImport test completed successfully!")

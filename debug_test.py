#!/usr/bin/env python3

"""
Debug test that writes output to a file
"""

import sys
import os
import traceback

# Redirect output to a file
with open('debug_output.txt', 'w') as f:
    try:
        f.write("Starting debug test...\n")
        f.flush()

        # Test basic imports
        f.write("Testing imports...\n")
        f.flush()

        from src.vanna.duckdb_nsql.duckdb_nsql_chat import DuckDBNSQL_Chat
        f.write("✓ DuckDBNSQL_Chat imported\n")
        f.flush()

        from src.vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
        f.write("✓ ChromaDB_VectorStore imported\n")
        f.flush()

        from chromadb.utils.embedding_functions import SentenceTransformerEmbeddingFunction
        f.write("✓ SentenceTransformerEmbeddingFunction imported\n")
        f.flush()

        # Test class definition
        f.write("Testing class definition...\n")
        f.flush()

        class TestVanna(DuckDBNSQL_Chat, ChromaDB_VectorStore):
            def __init__(self, config=None):
                if config is None:
                    config = {}

                config["temperature"] = 0.1
                config["max_new_tokens"] = 1024
                config["path"] = "./test_chromadb_store"
                config["client"] = "in-memory"

                # Initialize ChromaDB first
                ChromaDB_VectorStore.__init__(self, config=config)

                # Try to initialize DuckDB chat
                try:
                    DuckDBNSQL_Chat.__init__(self, config=config)
                except Exception as e:
                    f.write(f"DuckDB init failed: {e}\n")
                    f.flush()
                    # Setup fallback
                    from vanna.base.base import VannaBase
                    VannaBase.__init__(self, config=config)
                    self.temperature = 0.1
                    self.max_new_tokens = 1024

            def system_message(self, message: str):
                return {"role": "system", "content": message}

            def user_message(self, message: str):
                return {"role": "user", "content": message}

            def assistant_message(self, message: str):
                return {"role": "assistant", "content": message}

            def submit_prompt(self, prompt, **kwargs):
                return "Mock response"

        f.write("✓ TestVanna class defined\n")
        f.flush()

        # Test instantiation
        f.write("Testing instantiation...\n")
        f.flush()

        # Create mock embedding function with correct interface
        class MockEmbedding:
            def __call__(self, input):  # Changed from 'texts' to 'input'
                return [[0.1] * 384 for _ in input]

        config = {
            "path": "./test_chromadb_store",
            "client": "in-memory",
            "embedding_function": MockEmbedding()
        }

        vn = TestVanna(config=config)
        f.write("✓ TestVanna instantiated successfully\n")
        f.flush()

        # Test methods
        f.write("Testing methods...\n")
        f.flush()

        msg = vn.system_message("test")
        f.write(f"✓ system_message: {msg}\n")
        f.flush()

        response = vn.submit_prompt([msg])
        f.write(f"✓ submit_prompt: {response}\n")
        f.flush()

        f.write("✅ All tests passed!\n")
        f.flush()

    except Exception as e:
        f.write(f"❌ Error: {e}\n")
        f.write(f"Traceback: {traceback.format_exc()}\n")
        f.flush()

print("Debug test completed. Check debug_output.txt for results.")

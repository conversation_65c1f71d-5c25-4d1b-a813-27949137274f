import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Import LlamaIndex components
from llama_index import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    Document,
    ServiceContext,
    StorageContext
)
from llama_index.node_parser import SentenceSplitter
from llama_index.embeddings import OpenAIEmbedding
from llama_index.llms import OpenAI
from llama_index.vector_stores import ChromaVectorStore
import chromadb

# For environment variables
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class LlamaIndexPDFStore:
    """
    A class to manage PDF documents using LlamaIndex with OpenAI embeddings.
    Provides functionality to add PDFs and query them using semantic search.
    """

    def __init__(
        self,
        persist_dir: str = "./pdf_store",
        collection_name: str = "pdf_documents",
        embedding_model: str = "text-embedding-3-small",
        llm_model: str = "gpt-3.5-turbo",
        chunk_size: int = 1000,
        chunk_overlap: int = 200
    ):
        """
        Initialize the PDF store with LlamaIndex, OpenAI embeddings, and ChromaDB.

        Args:
            persist_dir (str): Directory to persist the vector store
            collection_name (str): Name of the collection
            embedding_model (str): OpenAI embedding model to use
            llm_model (str): OpenAI LLM model to use
            chunk_size (int): Size of text chunks for splitting documents
            chunk_overlap (int): Overlap between chunks
        """
        self.persist_dir = persist_dir
        self.collection_name = collection_name
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

        # Check for OpenAI API key
        if not os.getenv("OPENAI_API_KEY"):
            raise ValueError("OPENAI_API_KEY environment variable is not set")

        # Create directory if it doesn't exist
        os.makedirs(self.persist_dir, exist_ok=True)

        # Initialize embedding model
        embed_model = OpenAIEmbedding(model=embedding_model)

        # Initialize LLM
        llm = OpenAI(model=llm_model)

        # Create service context
        self.service_context = ServiceContext.from_defaults(
            llm=llm,
            embed_model=embed_model
        )

        # Initialize node parser for text splitting
        self.node_parser = SentenceSplitter(
            chunk_size=self.chunk_size,
            chunk_overlap=self.chunk_overlap
        )

        # Initialize ChromaDB client
        self.chroma_client = chromadb.PersistentClient(path=self.persist_dir)

        # Create or get collection
        self.chroma_collection = self.chroma_client.get_or_create_collection(
            name=self.collection_name
        )

        # Initialize vector store
        self.vector_store = ChromaVectorStore(chroma_collection=self.chroma_collection)

        # Initialize storage context
        self.storage_context = StorageContext.from_defaults(vector_store=self.vector_store)

        # Initialize or load the index
        try:
            self.index = VectorStoreIndex.from_vector_store(
                vector_store=self.vector_store,
                service_context=self.service_context
            )
            logging.info(f"Loaded existing index from {self.persist_dir}")
        except Exception as e:
            logging.info(f"Creating new index: {str(e)}")
            self.index = VectorStoreIndex(
                [],
                storage_context=self.storage_context,
                service_context=self.service_context
            )
            self.persist()

    def add_pdf(self, pdf_path: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Add a PDF document to the index.

        Args:
            pdf_path (str): Path to the PDF file
            metadata (dict, optional): Additional metadata for the document

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if file exists and is a PDF
            path = Path(pdf_path)
            if not path.exists():
                logging.error(f"File not found: {pdf_path}")
                return False
            if path.suffix.lower() != ".pdf":
                logging.error(f"Not a PDF file: {pdf_path}")
                return False

            # Load the document
            logging.info(f"Loading PDF: {pdf_path}")
            documents = SimpleDirectoryReader(input_files=[pdf_path]).load_data()

            # Add metadata if provided
            if metadata:
                for doc in documents:
                    doc.metadata.update(metadata)

            # Parse documents into nodes
            nodes = self.node_parser.get_nodes_from_documents(documents)
            logging.info(f"Split PDF into {len(nodes)} nodes")

            # Insert nodes into the index
            self.index.insert_nodes(nodes, service_context=self.service_context)

            # Persist the index
            self.persist()

            logging.info(f"Successfully added PDF: {pdf_path}")
            return True

        except Exception as e:
            logging.error(f"Error adding PDF: {str(e)}")
            return False

    def add_pdf_directory(self, directory_path: str, metadata: Optional[Dict[str, Any]] = None) -> int:
        """
        Add all PDF documents from a directory to the index.

        Args:
            directory_path (str): Path to the directory containing PDF files
            metadata (dict, optional): Additional metadata for all documents

        Returns:
            int: Number of PDFs successfully added
        """
        try:
            # Check if directory exists
            dir_path = Path(directory_path)
            if not dir_path.is_dir():
                logging.error(f"Directory not found: {directory_path}")
                return 0

            # Find all PDF files in the directory
            pdf_files = list(dir_path.glob("**/*.pdf"))

            if not pdf_files:
                logging.info(f"No PDF files found in {directory_path}")
                return 0

            logging.info(f"Found {len(pdf_files)} PDF files in {directory_path}")

            # Add each PDF file
            successful_count = 0
            for pdf_file in pdf_files:
                file_metadata = metadata.copy() if metadata else {}
                file_metadata["source_directory"] = str(directory_path)

                if self.add_pdf(str(pdf_file), file_metadata):
                    successful_count += 1

            logging.info(f"Successfully added {successful_count} out of {len(pdf_files)} PDFs")
            return successful_count

        except Exception as e:
            logging.error(f"Error adding PDF directory: {str(e)}")
            return 0

    def query(self, query_text: str, top_k: int = 5) -> Dict[str, Any]:
        """
        Query the index with a natural language query.

        Args:
            query_text (str): The query text
            top_k (int): Number of top results to return

        Returns:
            dict: Query results including response and source documents
        """
        try:
            # Create query engine
            query_engine = self.index.as_query_engine(
                similarity_top_k=top_k,
                service_context=self.service_context
            )

            # Execute query
            logging.info(f"Executing query: {query_text}")
            response = query_engine.query(query_text)

            # Extract source documents and their metadata
            source_documents = []
            if hasattr(response, 'source_nodes'):
                for source_node in response.source_nodes:
                    try:
                        text = source_node.node.get_content()
                        metadata = source_node.node.metadata
                        score = source_node.score
                    except AttributeError:
                        # Handle different response structure in newer versions
                        text = getattr(source_node, 'text', '') or getattr(source_node, 'content', '')
                        metadata = getattr(source_node, 'metadata', {}) or getattr(source_node, 'extra_info', {})
                        score = getattr(source_node, 'score', None)

                    source_info = {
                        "text": text,
                        "metadata": metadata,
                        "score": score
                    }
                    source_documents.append(source_info)

            # Return results
            return {
                "response": str(response),
                "source_documents": source_documents
            }

        except Exception as e:
            logging.error(f"Error executing query: {str(e)}")
            return {"error": str(e)}

    def persist(self):
        """
        Persist the index to disk.
        """
        if hasattr(self.index, 'storage_context'):
            self.index.storage_context.persist(persist_dir=self.persist_dir)
        else:
            self.storage_context.persist(persist_dir=self.persist_dir)
        logging.info(f"Index persisted to {self.persist_dir}")

    def get_document_count(self) -> int:
        """
        Get the number of documents in the index.

        Returns:
            int: Number of documents
        """
        try:
            return self.chroma_collection.count()
        except Exception as e:
            logging.error(f"Error getting document count: {str(e)}")
            return 0

# Example usage
if __name__ == "__main__":
    try:
        # Check for OpenAI API key
        if not os.getenv("OPENAI_API_KEY"):
            print("Please set OPENAI_API_KEY environment variable or in .env file")
            sys.exit(1)

        # Initialize the PDF store
        print("Initializing LlamaIndexPDFStore...")
        pdf_store = LlamaIndexPDFStore()

        # Example: Add a PDF if path is provided
        if len(sys.argv) > 1 and sys.argv[1].endswith('.pdf'):
            pdf_path = sys.argv[1]
            print(f"Adding PDF: {pdf_path}")
            pdf_store.add_pdf(pdf_path, {"category": "user_provided"})

        # Print document count
        print(f"Total documents in index: {pdf_store.get_document_count()}")

        # Example: Query the index
        if len(sys.argv) > 2:
            query = sys.argv[2]
            print(f"Querying: {query}")
            results = pdf_store.query(query)

            print("\nResponse:\n")
            print(results["response"])

            print("\nSources:\n")
            for i, source in enumerate(results.get("source_documents", [])):
                print(f"Source {i+1}:\n{source['text'][:200]}...\n")
                print(f"Source metadata: {source['metadata']}\n")
        else:
            print("\nTo query the PDF store, run: python llamaindex_pdf_store_openai.py [pdf_path] \"your question here\"")

    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

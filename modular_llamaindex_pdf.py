import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# For environment variables
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Check for OpenAI API key
if not os.getenv("OPENAI_API_KEY"):
    print("Please set OPENAI_API_KEY environment variable or in .env file")
    sys.exit(1)

try:
    # Import LlamaIndex components (modular approach)
    from llama_index.core import (
        VectorStoreIndex,
        StorageContext,
        Settings,
    )
    from llama_index.core.node_parser import SentenceWindowNodeParser
    from llama_index.llms.openai import OpenAI
    from llama_index.embeddings.openai import OpenAIEmbedding
    from llama_index.vector_stores.chroma import ChromaVectorStore
    from llama_index.readers.file import PyPDFReader
    import chromadb
except ImportError as e:
    print(f"Error importing LlamaIndex components: {e}")
    print("Please install the required packages with: uv pip install llama-index-llms-openai llama-index-embeddings-openai llama-index-readers-file llama-index-vector-stores-chroma")
    sys.exit(1)

def setup_llama_index(
    persist_dir: str = "./pdf_store", 
    collection_name: str = "pdf_documents",
    embedding_model: str = "text-embedding-3-small",
    llm_model: str = "gpt-3.5-turbo",
):
    """
    Set up LlamaIndex with OpenAI embeddings and LLM.
    
    Args:
        persist_dir (str): Directory to persist the vector store
        collection_name (str): Name of the collection
        embedding_model (str): OpenAI embedding model to use
        llm_model (str): OpenAI LLM model to use
        
    Returns:
        tuple: (vector_store, storage_context, chroma_collection)
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(persist_dir, exist_ok=True)
        
        # Set up global settings
        Settings.llm = OpenAI(model=llm_model)
        Settings.embed_model = OpenAIEmbedding(model=embedding_model)
        
        # Initialize ChromaDB client
        chroma_client = chromadb.PersistentClient(path=persist_dir)
        
        # Create or get collection
        chroma_collection = chroma_client.get_or_create_collection(
            name=collection_name
        )
        
        # Initialize vector store
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
        
        # Initialize storage context
        storage_context = StorageContext.from_defaults(vector_store=vector_store)
        
        return vector_store, storage_context, chroma_collection
        
    except Exception as e:
        logging.error(f"Error setting up LlamaIndex: {str(e)}")
        raise

def load_pdf(pdf_path: str, metadata: Optional[Dict[str, Any]] = None):
    """
    Load a PDF document.
    
    Args:
        pdf_path (str): Path to the PDF file
        metadata (dict, optional): Additional metadata for the document
        
    Returns:
        list: List of document objects
    """
    try:
        # Check if file exists and is a PDF
        path = Path(pdf_path)
        if not path.exists():
            logging.error(f"File not found: {pdf_path}")
            return []
        if path.suffix.lower() != ".pdf":
            logging.error(f"Not a PDF file: {pdf_path}")
            return []
        
        # Load the document
        logging.info(f"Loading PDF: {pdf_path}")
        reader = PyPDFReader()
        documents = reader.load_data(file=pdf_path)
        
        # Add metadata if provided
        if metadata:
            for doc in documents:
                doc.metadata.update(metadata)
        
        return documents
        
    except Exception as e:
        logging.error(f"Error loading PDF: {str(e)}")
        return []

def create_index_from_documents(documents, storage_context):
    """
    Create an index from documents.
    
    Args:
        documents: List of document objects
        storage_context: Storage context
        
    Returns:
        VectorStoreIndex: The created index
    """
    try:
        # Parse documents into nodes
        node_parser = SentenceWindowNodeParser.from_defaults(
            window_size=3,
            window_metadata_key="window",
            original_text_metadata_key="original_text",
        )
        nodes = node_parser.get_nodes_from_documents(documents)
        logging.info(f"Split documents into {len(nodes)} nodes")
        
        # Create index
        index = VectorStoreIndex(
            nodes, 
            storage_context=storage_context
        )
        
        return index
        
    except Exception as e:
        logging.error(f"Error creating index: {str(e)}")
        raise

def query_index(index, query_text: str, top_k: int = 5):
    """
    Query the index.
    
    Args:
        index: The index to query
        query_text (str): The query text
        top_k (int): Number of top results to return
        
    Returns:
        dict: Query results
    """
    try:
        # Create query engine
        query_engine = index.as_query_engine(
            similarity_top_k=top_k
        )
        
        # Execute query
        logging.info(f"Executing query: {query_text}")
        response = query_engine.query(query_text)
        
        # Extract source documents
        source_documents = []
        if hasattr(response, 'source_nodes'):
            for source_node in response.source_nodes:
                source_info = {
                    "text": source_node.get_content(),
                    "metadata": source_node.metadata,
                    "score": source_node.score if hasattr(source_node, 'score') else None
                }
                source_documents.append(source_info)
        
        # Return results
        return {
            "response": str(response),
            "source_documents": source_documents
        }
        
    except Exception as e:
        logging.error(f"Error querying index: {str(e)}")
        return {"error": str(e)}

def get_document_count(chroma_collection):
    """
    Get the number of documents in the collection.
    
    Args:
        chroma_collection: The ChromaDB collection
        
    Returns:
        int: Number of documents
    """
    try:
        return chroma_collection.count()
    except Exception as e:
        logging.error(f"Error getting document count: {str(e)}")
        return 0

# Example usage
if __name__ == "__main__":
    try:
        # Set up LlamaIndex
        print("Setting up LlamaIndex...")
        vector_store, storage_context, chroma_collection = setup_llama_index()
        
        # Check if a PDF path is provided
        if len(sys.argv) > 1 and sys.argv[1].endswith('.pdf'):
            pdf_path = sys.argv[1]
            print(f"Loading PDF: {pdf_path}")
            
            # Load the PDF
            documents = load_pdf(pdf_path, {"category": "user_provided"})
            if not documents:
                print("Failed to load PDF.")
                sys.exit(1)
            
            # Create index
            print("Creating index...")
            index = create_index_from_documents(documents, storage_context)
            
            # Print document count
            print(f"Total documents in collection: {get_document_count(chroma_collection)}")
            
            # Query if provided
            if len(sys.argv) > 2:
                query = sys.argv[2]
                print(f"Querying: {query}")
                results = query_index(index, query)
                
                print("\nResponse:\n")
                print(results["response"])
                
                print("\nSources:\n")
                for i, source in enumerate(results.get("source_documents", [])):
                    print(f"Source {i+1}:\n{source['text'][:200]}...\n")
                    print(f"Source metadata: {source['metadata']}\n")
            else:
                print("\nTo query the PDF, run: python modular_llamaindex_pdf.py [pdf_path] \"your question here\"")
        else:
            print("Please provide a PDF path as the first argument.")
            print("Usage: python modular_llamaindex_pdf.py [pdf_path] \"your question here\"")
    
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

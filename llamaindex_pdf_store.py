import os
import sys
import logging
import PyPDF2
import re
import json
from pathlib import Path
from typing import List, Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Import OpenAI for embeddings
import openai

# For vector database
import chromadb

# For environment variables
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Configure OpenAI client
openai.api_key = os.getenv("OPENAI_API_KEY")

class PDFVectorStore:
    """
    A class to manage PDF documents using OpenAI embeddings and ChromaDB.
    Provides functionality to add PDFs and query them using semantic search.
    """
    
    def __init__(self, persist_dir: str = "./pdf_store", collection_name: str = "pdf_documents"):
        """
        Initialize the PDF store with OpenAI embeddings and ChromaDB.
        
        Args:
            persist_dir (str): Directory to persist the ChromaDB
            collection_name (str): Name of the ChromaDB collection
        """
        self.persist_dir = persist_dir
        self.collection_name = collection_name
        self.chunk_size = 1000
        self.chunk_overlap = 200
        
        # Create directory if it doesn't exist
        os.makedirs(self.persist_dir, exist_ok=True)
        
        # Initialize ChromaDB client
        self.chroma_client = chromadb.PersistentClient(path=self.persist_dir)
        
        # Create or get collection
        self.collection = self.chroma_client.get_or_create_collection(
            name=self.collection_name,
            metadata={"hnsw:space": "cosine"}
        )
        
        logging.info(f"Initialized PDF Vector Store with collection: {self.collection_name}")
    
    def _get_embedding(self, text: str) -> List[float]:
        """
        Get OpenAI embedding for a text.
        
        Args:
            text (str): Text to embed
            
        Returns:
            List[float]: Embedding vector
        """
        try:
            response = openai.Embedding.create(
                input=text,
                model="text-embedding-3-small"
            )
            return response["data"][0]["embedding"]
        except Exception as e:
            logging.error(f"Error getting embedding: {str(e)}")
            raise
    
    def _extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        Extract text from a PDF file.
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            str: Extracted text
        """
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text()
                
                # Clean the text
                text = re.sub(r'\s+', ' ', text).strip()
                return text
        except Exception as e:
            logging.error(f"Error extracting text from PDF: {str(e)}")
            raise
    
    def _chunk_text(self, text: str) -> List[str]:
        """
        Split text into chunks with overlap.
        
        Args:
            text (str): Text to split
            
        Returns:
            List[str]: List of text chunks
        """
        chunks = []
        for i in range(0, len(text), self.chunk_size - self.chunk_overlap):
            chunk = text[i:i + self.chunk_size]
            if len(chunk) > 100:  # Only add chunks with substantial content
                chunks.append(chunk)
        return chunks
    
    def add_pdf(self, pdf_path: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Add a PDF document to the vector store.
        
        Args:
            pdf_path (str): Path to the PDF file
            metadata (dict, optional): Additional metadata for the document
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if file exists and is a PDF
            path = Path(pdf_path)
            if not path.exists():
                logging.error(f"File not found: {pdf_path}")
                return False
            if path.suffix.lower() != ".pdf":
                logging.error(f"Not a PDF file: {pdf_path}")
                return False
            
            # Extract text from PDF
            logging.info(f"Extracting text from PDF: {pdf_path}")
            text = self._extract_text_from_pdf(pdf_path)
            
            # Split text into chunks
            chunks = self._chunk_text(text)
            logging.info(f"Split PDF into {len(chunks)} chunks")
            
            # Prepare metadata
            doc_metadata = {
                "source": pdf_path,
                "filename": path.name
            }
            if metadata:
                doc_metadata.update(metadata)
            
            # Add chunks to collection
            for i, chunk in enumerate(chunks):
                chunk_id = f"{path.stem}_{i}"
                chunk_metadata = doc_metadata.copy()
                chunk_metadata["chunk_index"] = i
                
                # Get embedding for chunk
                embedding = self._get_embedding(chunk)
                
                # Add to collection
                self.collection.add(
                    ids=[chunk_id],
                    embeddings=[embedding],
                    metadatas=[chunk_metadata],
                    documents=[chunk]
                )
                
                if (i + 1) % 10 == 0 or (i + 1) == len(chunks):
                    logging.info(f"Added {i + 1}/{len(chunks)} chunks")
            
            logging.info(f"Successfully added PDF: {pdf_path}")
            return True
            
        except Exception as e:
            logging.error(f"Error adding PDF: {str(e)}")
            return False
    
    def add_pdf_directory(self, directory_path: str, metadata: Optional[Dict[str, Any]] = None) -> int:
        """
        Add all PDF documents from a directory to the vector store.
        
        Args:
            directory_path (str): Path to the directory containing PDF files
            metadata (dict, optional): Additional metadata for all documents
            
        Returns:
            int: Number of PDFs successfully added
        """
        try:
            # Check if directory exists
            dir_path = Path(directory_path)
            if not dir_path.is_dir():
                logging.error(f"Directory not found: {directory_path}")
                return 0
            
            # Find all PDF files in the directory
            pdf_files = list(dir_path.glob("**/*.pdf"))
            
            if not pdf_files:
                logging.info(f"No PDF files found in {directory_path}")
                return 0
            
            logging.info(f"Found {len(pdf_files)} PDF files in {directory_path}")
            
            # Add each PDF file
            successful_count = 0
            for pdf_file in pdf_files:
                file_metadata = metadata.copy() if metadata else {}
                file_metadata["source_directory"] = str(directory_path)
                
                if self.add_pdf(str(pdf_file), file_metadata):
                    successful_count += 1
            
            logging.info(f"Successfully added {successful_count} out of {len(pdf_files)} PDFs")
            return successful_count
            
        except Exception as e:
            logging.error(f"Error adding PDF directory: {str(e)}")
            return 0
    
    def query(self, query_text: str, top_k: int = 5) -> Dict[str, Any]:
        """
        Query the vector store with a natural language query.
        
        Args:
            query_text (str): The query text
            top_k (int): Number of top results to return
            
        Returns:
            dict: Query results including response and source documents
        """
        try:
            # Get embedding for query
            query_embedding = self._get_embedding(query_text)
            
            # Query collection
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=top_k,
                include=["documents", "metadatas", "distances"]
            )
            
            # Format results
            documents = []
            for i in range(len(results["ids"][0])):
                doc = {
                    "text": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "score": 1.0 - results["distances"][0][i]  # Convert distance to similarity score
                }
                documents.append(doc)
            
            # Generate response using OpenAI
            context = "\n".join([f"Context {i+1}:\n{doc['text']}" for i, doc in enumerate(documents)])
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a helpful assistant. Answer the question based on the provided context."},
                    {"role": "user", "content": f"Context information is below.\n\n{context}\n\nQuestion: {query_text}\n\nAnswer the question based on the context provided. If the answer cannot be found in the context, say 'I don't have enough information to answer this question.'"}
                ]
            )
            
            answer = response.choices[0].message["content"]
            
            return {
                "response": answer,
                "source_documents": documents
            }
            
        except Exception as e:
            logging.error(f"Error executing query: {str(e)}")
            return {"error": str(e)}
    
    def get_document_count(self) -> int:
        """
        Get the number of documents in the collection.
        
        Returns:
            int: Number of documents
        """
        return self.collection.count()

# Example usage
if __name__ == "__main__":
    try:
        # Check for OpenAI API key
        if not os.getenv("OPENAI_API_KEY"):
            print("Please set OPENAI_API_KEY environment variable or in .env file")
            sys.exit(1)
        
        # Initialize the PDF store
        print("Initializing PDF Vector Store...")
        pdf_store = PDFVectorStore()
        
        # Add a PDF file if specified
        pdf_path = "/Users/<USER>/Downloads/智慧醫療中心技術手冊.pdf"
        if os.path.exists(pdf_path):
            print(f"Adding PDF: {pdf_path}")
            pdf_store.add_pdf(pdf_path, {"category": "technical_docs"})
        
        # Print document count
        print(f"Total documents in collection: {pdf_store.get_document_count()}")
        
        # Query if argument is provided
        if len(sys.argv) > 1:
            query = sys.argv[1]
            print(f"Querying: {query}")
            results = pdf_store.query(query)
            
            print("\nResponse:\n")
            print(results["response"])
            
            print("\nSources:\n")
            for i, doc in enumerate(results.get("source_documents", [])):
                print(f"Source {i+1}:\n{doc['text'][:200]}...\n")
                print(f"Source file: {doc['metadata'].get('filename', 'Unknown')}\n")
        else:
            print("\nTo query the PDF store, run: python llamaindex_pdf_store.py \"your question here\"")
    
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
# Note: This file provides a standalone PDF vector store implementation
# that doesn't rely on LlamaIndex, using OpenAI embeddings and ChromaDB directly
        
        # Create directory if it doesn't exist
        os.makedirs(self.persist_dir, exist_ok=True)
        
        # Initialize ChromaDB client and collection
        try:
            self.chroma_client = chromadb.PersistentClient(path=self.persist_dir)
        except TypeError:
            # Fallback for older versions
            self.chroma_client = chromadb.Client(chromadb.Settings(
                chroma_db_impl="duckdb+parquet",
                persist_directory=self.persist_dir
            ))
        
        # Try to get collection or create if it doesn't exist
        try:
            self.chroma_collection = self.chroma_client.get_or_create_collection(name=self.collection_name)
            logging.info(f"Using existing collection: {self.collection_name}")
        except Exception as e:
            logging.error(f"Error getting/creating collection: {str(e)}")
            raise
        
        # Create vector store and storage context
        try:
            self.vector_store = ChromaVectorStore(chroma_collection=self.chroma_collection)
        except TypeError:
            # Fallback for different constructor signature
            self.vector_store = ChromaVectorStore(chroma_collection=self.chroma_collection, embedding_function=embed_model)
            
        try:
            self.storage_context = StorageContext.from_defaults(vector_store=self.vector_store)
        except TypeError:
            # Alternative for newer versions
            self.storage_context = StorageContext.from_defaults({"vector_store": self.vector_store})
        
        # Initialize or load the index
        try:
            try:
                self.index = load_index_from_storage(self.storage_context)
            except TypeError:
                # Alternative for newer versions
                self.index = load_index_from_storage(storage_context=self.storage_context)
            logging.info("Loaded existing index")
        except Exception as e:
            logging.info(f"No existing index found, creating a new one: {str(e)}")
            try:
                self.index = VectorStoreIndex.from_documents(
                    [], storage_context=self.storage_context
                )
            except TypeError:
                # Alternative for newer versions
                self.index = VectorStoreIndex([], storage_context=self.storage_context)
            self.persist()
    
    def add_pdf(self, pdf_path: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Add a PDF document to the index.
        
        Args:
            pdf_path (str): Path to the PDF file
            metadata (dict, optional): Additional metadata for the document
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Check if file exists and is a PDF
            path = Path(pdf_path)
            if not path.exists():
                logging.error(f"File not found: {pdf_path}")
                return False
            if path.suffix.lower() != ".pdf":
                logging.error(f"Not a PDF file: {pdf_path}")
                return False
            
            # Load the document
            logging.info(f"Loading PDF: {pdf_path}")
            try:
                # Try with newer LlamaIndex API
                documents = SimpleDirectoryReader(input_files=[pdf_path]).load_data()
            except TypeError:
                # Fallback for older versions
                documents = SimpleDirectoryReader(input_files=[str(path)]).load_data()
            
            # Add metadata if provided
            if metadata:
                for doc in documents:
                    if hasattr(doc, 'metadata'):
                        doc.metadata.update(metadata)
                    else:
                        # Handle different document structure in newer versions
                        doc.extra_info = metadata if not hasattr(doc, 'extra_info') else {**doc.extra_info, **metadata}
            
            # Insert documents into the index
            try:
                self.index.insert_documents(documents)
            except AttributeError:
                # Alternative method for newer versions
                for doc in documents:
                    self.index.insert(doc)
            
            # Persist the index
            self.persist()
            
            logging.info(f"Successfully added PDF: {pdf_path}")
            return True
            
        except Exception as e:
            logging.error(f"Error adding PDF: {str(e)}")
            return False
    
    def add_pdf_directory(self, directory_path: str, metadata: Optional[Dict[str, Any]] = None) -> int:
        """
        Add all PDF documents from a directory to the index.
        
        Args:
            directory_path (str): Path to the directory containing PDF files
            metadata (dict, optional): Additional metadata for all documents
            
        Returns:
            int: Number of PDFs successfully added
        """
        try:
            # Check if directory exists
            dir_path = Path(directory_path)
            if not dir_path.is_dir():
                logging.error(f"Directory not found: {directory_path}")
                return 0
            
            # Find all PDF files in the directory
            pdf_files = list(dir_path.glob("**/*.pdf"))
            
            if not pdf_files:
                logging.info(f"No PDF files found in {directory_path}")
                return 0
            
            logging.info(f"Found {len(pdf_files)} PDF files in {directory_path}")
            
            # Add each PDF file
            successful_count = 0
            for pdf_file in pdf_files:
                file_metadata = metadata.copy() if metadata else {}
                file_metadata["source_directory"] = directory_path
                
                if self.add_pdf(str(pdf_file), file_metadata):
                    successful_count += 1
            
            logging.info(f"Successfully added {successful_count} out of {len(pdf_files)} PDFs")
            return successful_count
            
        except Exception as e:
            logging.error(f"Error adding PDF directory: {str(e)}")
            return 0
    
    def query(self, query_text: str, top_k: int = 5) -> Dict[str, Any]:
        """
        Query the index with a natural language query.
        
        Args:
            query_text (str): The query text
            top_k (int): Number of top results to return
            
        Returns:
            dict: Query results including response and source documents
        """
        try:
            # Create query engine
            try:
                query_engine = self.index.as_query_engine(similarity_top_k=top_k)
            except (TypeError, AttributeError):
                # Alternative for newer versions
                query_engine = self.index.as_query_engine(similarity_k=top_k)
            
            # Execute query
            logging.info(f"Executing query: {query_text}")
            response = query_engine.query(query_text)
            
            # Extract source documents and their metadata
            source_documents = []
            try:
                source_nodes = response.source_nodes
            except AttributeError:
                # Alternative for newer versions
                source_nodes = getattr(response, 'source_nodes', [])
                if not source_nodes and hasattr(response, 'metadata'):
                    source_nodes = response.metadata.get('source_nodes', [])
            
            for source_node in source_nodes:
                try:
                    text = source_node.node.get_content()
                    metadata = source_node.node.metadata
                except AttributeError:
                    # Alternative for newer versions
                    text = getattr(source_node, 'text', '') or getattr(source_node, 'content', '')
                    metadata = getattr(source_node, 'metadata', {}) or getattr(source_node, 'extra_info', {})
                
                source_info = {
                    "text": text,
                    "metadata": metadata,
                    "score": getattr(source_node, 'score', None)
                }
                source_documents.append(source_info)
            
            # Return results
            return {
                "response": str(response),
                "source_documents": source_documents
            }
            
        except Exception as e:
            logging.error(f"Error executing query: {str(e)}")
            return {"error": str(e)}
    
    def persist(self):
        """
        Persist the index to disk.
        """
        self.index.storage_context.persist(persist_dir=self.persist_dir)
        logging.info(f"Index persisted to {self.persist_dir}")
    
    def get_document_count(self) -> int:
        """
        Get the number of documents in the index.
        
        Returns:
            int: Number of documents
        """
        try:
            return len(self.chroma_collection.get(include=[])['ids'])
        except (TypeError, KeyError):
            # Alternative for newer versions
            try:
                return self.chroma_collection.count()
            except AttributeError:
                # Last resort fallback
                return len(self.chroma_collection.get()['ids'] if 'ids' in self.chroma_collection.get() else [])

# Example usage
if __name__ == "__main__":
    # Make sure to set OPENAI_API_KEY in your environment or .env file
    if not os.getenv("OPENAI_API_KEY"):
        print("Please set OPENAI_API_KEY environment variable or in .env file")
        sys.exit(1)
    
    try:
        # Initialize the PDF store
        print("Initializing LlamaIndexPDFStore...")
        pdf_store = LlamaIndexPDFStore()
        
        # Example: Add a single PDF
        print("Adding PDF document...")
        pdf_store.add_pdf("/Users/<USER>/Downloads/智慧醫療中心技術手冊.pdf", {"category": "technical_docs"})
        
        # Example: Add all PDFs from a directory
        # pdf_store.add_pdf_directory("/path/to/pdf/directory", {"category": "technical_docs"})
        
        # Print document count
        print(f"Total documents in index: {pdf_store.get_document_count()}")
        
        # Example: Query the index
        print("\nTry querying the document with: python llamaindex_pdf_store.py \"your question here\"")
        if len(sys.argv) > 1:
            query = sys.argv[1]
            print(f"Querying: {query}")
            results = pdf_store.query(query)
            print("\nResponse:\n", results["response"])
            print("\nSources:\n")
            for i, source in enumerate(results.get("source_documents", [])):
                print(f"Source {i+1}:\n{source['text'][:200]}...\n")
    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()

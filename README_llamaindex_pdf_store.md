# LlamaIndex PDF Store with OpenAI Embeddings

This implementation provides a robust solution for storing, indexing, and querying PDF documents using LlamaIndex and OpenAI embeddings.

## Features

- Load and process PDF documents
- Store embeddings and metadata in a vector database (ChromaDB)
- Query documents using natural language
- Retrieve relevant context and sources for each query
- Support for adding metadata to documents
- Batch processing of PDF directories

## Requirements

- Python 3.8+
- OpenAI API key
- Required packages (install with `uv`):

```bash
uv pip install llama-index llama-index-embeddings-openai llama-index-llms-openai llama-index-vector-stores-chroma chromadb python-dotenv
```

## Usage

### Basic Usage

1. Set your OpenAI API key:
   ```bash
   export OPENAI_API_KEY="your-api-key"
   ```
   
   Or create a `.env` file with:
   ```
   OPENAI_API_KEY=your-api-key
   ```

2. Add a PDF and query it:
   ```bash
   python llamaindex_pdf_store_openai.py path/to/your/document.pdf "Your question about the document"
   ```

### Python API

```python
from llamaindex_pdf_store_openai import LlamaIndexPDFStore

# Initialize the store
pdf_store = LlamaIndexPDFStore(
    persist_dir="./my_pdf_store",
    collection_name="my_documents",
    embedding_model="text-embedding-3-small",
    llm_model="gpt-3.5-turbo"
)

# Add a PDF with metadata
pdf_store.add_pdf("path/to/document.pdf", {"category": "research", "author": "John Doe"})

# Add all PDFs from a directory
pdf_store.add_pdf_directory("path/to/pdf/directory", {"category": "technical_docs"})

# Query the documents
results = pdf_store.query("What is the main conclusion of the research?")

# Print the response
print(results["response"])

# Print the sources
for i, source in enumerate(results["source_documents"]):
    print(f"Source {i+1}: {source['text'][:100]}...")
    print(f"Metadata: {source['metadata']}")
```

## Customization

You can customize the following parameters when initializing the PDF store:

- `persist_dir`: Directory to persist the vector store
- `collection_name`: Name of the ChromaDB collection
- `embedding_model`: OpenAI embedding model to use
- `llm_model`: OpenAI LLM model to use
- `chunk_size`: Size of text chunks for splitting documents
- `chunk_overlap`: Overlap between chunks

## How It Works

1. **Document Loading**: PDFs are loaded using LlamaIndex's SimpleDirectoryReader
2. **Text Chunking**: Documents are split into manageable chunks using SentenceSplitter
3. **Embedding Generation**: OpenAI's embedding model converts text chunks into vector representations
4. **Vector Storage**: Embeddings and metadata are stored in ChromaDB
5. **Querying**: Natural language queries are converted to embeddings and semantically similar chunks are retrieved
6. **Response Generation**: LlamaIndex uses the retrieved chunks and OpenAI's LLM to generate a comprehensive response

## Advanced Features

### Metadata Filtering

You can implement metadata filtering by extending the query method:

```python
from llama_index.core.vector_stores.types import MetadataFilters, MetadataFilter

def query_with_filters(self, query_text: str, filters: Dict[str, Any], top_k: int = 5):
    metadata_filters = MetadataFilters(
        filters=[
            MetadataFilter(key=k, value=v) for k, v in filters.items()
        ]
    )
    
    query_engine = self.index.as_query_engine(
        similarity_top_k=top_k,
        filters=metadata_filters
    )
    
    response = query_engine.query(query_text)
    # ... rest of the query method
```

### Custom Embedding Models

You can use different embedding models by changing the initialization:

```python
from llama_index.embeddings.huggingface import HuggingFaceEmbedding

embed_model = HuggingFaceEmbedding(model_name="BAAI/bge-small-en-v1.5")
Settings.embed_model = embed_model
```

## Troubleshooting

- **OpenAI API Key Issues**: Ensure your API key is correctly set in the environment or .env file
- **PDF Loading Errors**: Make sure the PDF is not corrupted and is accessible
- **Memory Issues**: For large PDFs, consider reducing the chunk size or processing files individually

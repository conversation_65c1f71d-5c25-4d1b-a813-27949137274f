import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# For environment variables
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Check for OpenAI API key
if not os.getenv("OPENAI_API_KEY"):
    print("Please set OPENAI_API_KEY environment variable or in .env file")
    sys.exit(1)

try:
    # Import LlamaIndex components for version 0.8.0
    from llama_index import (
        VectorStoreIndex,
        SimpleDirectoryReader,
        ServiceContext,
        StorageContext,
        LLMPredictor,
        PromptHelper,
        OpenAIEmbedding
    )
    from llama_index.node_parser import SimpleNodeParser
    from llama_index.langchain_helpers.chain_wrapper import LLMChain
    from langchain.chat_models import ChatOpenAI
    from llama_index.vector_stores import ChromaVectorStore
    import chromadb
except ImportError as e:
    print(f"Error importing LlamaIndex components: {e}")
    print("Please install the required packages with: uv pip install llama-index==0.8.0 openai python-dotenv chromadb")
    sys.exit(1)

def create_pdf_store(
    persist_dir: str = "./pdf_store",
    collection_name: str = "pdf_documents",
    embedding_model: str = "text-embedding-3-small",
    llm_model: str = "gpt-3.5-turbo",
    chunk_size: int = 1000,
    chunk_overlap: int = 200
):
    """
    Create a PDF store with LlamaIndex, OpenAI embeddings, and ChromaDB.

    Args:
        persist_dir (str): Directory to persist the vector store
        collection_name (str): Name of the collection
        embedding_model (str): OpenAI embedding model to use
        llm_model (str): OpenAI LLM model to use
        chunk_size (int): Size of text chunks for splitting documents
        chunk_overlap (int): Overlap between chunks

    Returns:
        tuple: (index, service_context, storage_context, chroma_collection)
    """
    try:
        # Create directory if it doesn't exist
        os.makedirs(persist_dir, exist_ok=True)

        # Initialize embedding model
        embed_model = OpenAIEmbedding(model_name=embedding_model)

        # Initialize LLM
        llm_predictor = LLMPredictor(llm=ChatOpenAI(temperature=0, model_name=llm_model))

        # Initialize prompt helper
        prompt_helper = PromptHelper(
            max_input_size=4096,
            num_output=256,
            max_chunk_overlap=chunk_overlap,
            chunk_size_limit=chunk_size
        )

        # Create service context
        service_context = ServiceContext.from_defaults(
            llm_predictor=llm_predictor,
            embed_model=embed_model,
            prompt_helper=prompt_helper
        )

        # Initialize node parser for text splitting
        node_parser = SimpleNodeParser.from_defaults()

        # Initialize ChromaDB client
        chroma_client = chromadb.PersistentClient(path=persist_dir)

        # Create or get collection
        chroma_collection = chroma_client.get_or_create_collection(
            name=collection_name
        )

        # Initialize vector store
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)

        # Initialize storage context
        storage_context = StorageContext.from_defaults(vector_store=vector_store)

        # Initialize or load the index
        try:
            index = VectorStoreIndex.from_vector_store(
                vector_store=vector_store,
                service_context=service_context
            )
            logging.info(f"Loaded existing index from {persist_dir}")
        except Exception as e:
            logging.info(f"Creating new index: {str(e)}")
            index = VectorStoreIndex(
                [],
                storage_context=storage_context,
                service_context=service_context
            )
            # Persist the index
            if hasattr(index, 'storage_context'):
                index.storage_context.persist(persist_dir=persist_dir)
            else:
                storage_context.persist(persist_dir=persist_dir)

        return index, service_context, storage_context, chroma_collection

    except Exception as e:
        logging.error(f"Error creating PDF store: {str(e)}")
        raise

def add_pdf(
    index,
    service_context,
    pdf_path: str,
    metadata: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Add a PDF document to the index.

    Args:
        index: The VectorStoreIndex
        service_context: The ServiceContext
        pdf_path (str): Path to the PDF file
        metadata (dict, optional): Additional metadata for the document

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Check if file exists and is a PDF
        path = Path(pdf_path)
        if not path.exists():
            logging.error(f"File not found: {pdf_path}")
            return False
        if path.suffix.lower() != ".pdf":
            logging.error(f"Not a PDF file: {pdf_path}")
            return False

        # Load the document
        logging.info(f"Loading PDF: {pdf_path}")
        documents = SimpleDirectoryReader(input_files=[pdf_path]).load_data()

        # Add metadata if provided
        if metadata:
            for doc in documents:
                doc.metadata.update(metadata)

        # Parse documents into nodes
        node_parser = SimpleNodeParser.from_defaults()
        nodes = node_parser.get_nodes_from_documents(documents)
        logging.info(f"Split PDF into {len(nodes)} nodes")

        # Insert nodes into the index
        for node in nodes:
            index.insert(node)

        # Persist the index
        if hasattr(index, 'storage_context'):
            index.storage_context.persist()

        logging.info(f"Successfully added PDF: {pdf_path}")
        return True

    except Exception as e:
        logging.error(f"Error adding PDF: {str(e)}")
        return False

def query_pdf(
    index,
    service_context,
    query_text: str,
    top_k: int = 5
) -> Dict[str, Any]:
    """
    Query the index with a natural language query.

    Args:
        index: The VectorStoreIndex
        service_context: The ServiceContext
        query_text (str): The query text
        top_k (int): Number of top results to return

    Returns:
        dict: Query results including response and source documents
    """
    try:
        # Create query engine
        query_engine = index.as_query_engine(
            similarity_top_k=top_k
        )

        # Execute query
        logging.info(f"Executing query: {query_text}")
        response = query_engine.query(query_text)

        # Extract source documents and their metadata
        source_documents = []
        if hasattr(response, 'source_nodes'):
            for source_node in response.source_nodes:
                source_info = {
                    "text": source_node.node.text,
                    "metadata": source_node.node.extra_info,
                    "score": source_node.score
                }
                source_documents.append(source_info)

        # Return results
        return {
            "response": str(response),
            "source_documents": source_documents
        }

    except Exception as e:
        logging.error(f"Error executing query: {str(e)}")
        return {"error": str(e)}

def get_document_count(chroma_collection) -> int:
    """
    Get the number of documents in the collection.

    Args:
        chroma_collection: The ChromaDB collection

    Returns:
        int: Number of documents
    """
    try:
        return chroma_collection.count()
    except Exception as e:
        logging.error(f"Error getting document count: {str(e)}")
        return 0

# Example usage
if __name__ == "__main__":
    try:
        # Initialize the PDF store
        print("Initializing PDF Vector Store...")
        index, service_context, storage_context, chroma_collection = create_pdf_store()

        # Example: Add a PDF if path is provided
        if len(sys.argv) > 1 and sys.argv[1].endswith('.pdf'):
            pdf_path = sys.argv[1]
            print(f"Adding PDF: {pdf_path}")
            add_pdf(index, service_context, pdf_path, {"category": "user_provided"})

        # Print document count
        print(f"Total documents in index: {get_document_count(chroma_collection)}")

        # Example: Query the index
        if len(sys.argv) > 2:
            query = sys.argv[2]
            print(f"Querying: {query}")
            results = query_pdf(index, service_context, query)

            print("\nResponse:\n")
            print(results["response"])

            print("\nSources:\n")
            for i, source in enumerate(results.get("source_documents", [])):
                print(f"Source {i+1}:\n{source['text'][:200]}...\n")
                print(f"Source metadata: {source['metadata']}\n")
        else:
            print("\nTo query the PDF store, run: python simple_llamaindex_pdf.py [pdf_path] \"your question here\"")

    except Exception as e:
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
